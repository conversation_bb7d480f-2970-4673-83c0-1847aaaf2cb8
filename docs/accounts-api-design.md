# Accounts API Design Document

## Table of Contents
1. [API Overview](#api-overview)
2. [Database Schema](#database-schema)
3. [GET /accounts API](#get-accounts-api)
4. [POST /accounts API](#post-accounts-api)
5. [Data Models](#data-models)
6. [Error Handling](#error-handling)
7. [Security](#security)
8. [Validation Rules](#validation-rules)
9. [Examples](#examples)
10. [Implementation Notes](#implementation-notes)

## API Overview

The Accounts API provides functionality to manage account entities within the Heal Control Center system. It supports:

- **GET /accounts**: Retrieve a list of all active accounts with their associated metadata
- **POST /accounts**: Create a new account with threshold severity settings and tags

### Base URL
```
{server}/v2.0/api/accounts
```

### Authentication
All endpoints require Bearer token authentication via the `Authorization` header.

## Database Schema

### Primary Tables

#### account_1 Table
```sql
CREATE TABLE `account_anomaly_configurations` (
`id` int NOT NULL AUTO_INCREMENT,
`account_id` int NOT NULL,
`user_details_id` varchar(256) NOT NULL,
`created_time` datetime NOT NULL,
`updated_time` datetime NOT NULL,
`low_enable` TINYINT NOT NULL DEFAULT '1',
`medium_enable` TINYINT NOT NULL DEFAULT '1',
`high_enable` TINYINT NOT NULL DEFAULT '1',
`closing_window` int NOT NULL DEFAULT '3',
`max_data_breaks` int NOT NULL DEFAULT '30',
PRIMARY KEY (`id`),
KEY `fk_account_anomaly_configurations_1_idx` (`account_id`),
CONSTRAINT `fk_account_anomaly_configurations_1` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`)
) ENGINE=InnoDB;
```

#### Legacy account Table (for GET operations)
```sql
CREATE TABLE account (
    id INT PRIMARY KEY,
    identifier VARCHAR(255),
    name VARCHAR(255),
    public_key TEXT,
    private_key TEXT,
    user_details_id VARCHAR(255),
    status INT,
    updated_time DATETIME,
    created_time DATETIME
);
```

## GET /accounts API

### Endpoint
```
GET /accounts
```

### Description
Retrieves a list of all active accounts with their metadata including timezone information.

### Request Headers
| Header | Type | Required | Description |
|--------|------|----------|-------------|
| Authorization | String | Yes | Bearer token for authentication |

### Response Structure
```json
{
    "message": "Accounts fetched successfully.",
    "data": [
        {
            "accountId": 1,
            "accountName": "Production Account",
            "status": 1,
            "privateKey": "-----BEGIN PRIVATE KEY-----...",
            "publicKey": "-----BEGIN PUBLIC KEY-----...",
            "tags": null,
            "timezoneMilli": ********,
            "timeZoneString": "Asia/Kolkata",
            "updatedTime": *************,
            "updatedBy": "admin",
            "dateFormat": "yyyy-MM-dd",
            "timeFormat": "HH:mm:ss",
            "identifier": "prod-account-001"
        }
    ],
    "responseStatus": "OK"
}
```

### Business Logic Flow
1. **Client Validation**: Validate Authorization header and extract user ID
2. **Server Validation**: Currently returns null (no additional validation)
3. **Data Processing**:
   - Fetch all accounts with status = 1 from `account` table
   - For each account, fetch timezone details from `mst_timezone` via tag mapping
   - Transform AccountBean to Account POJO
   - Handle timezone defaults if not found

### Error Scenarios
- **401 Unauthorized**: Invalid or missing authorization token
- **500 Internal Server Error**: Database connection issues or data processing errors

## POST /accounts API

### Endpoint
```
POST /accounts
```

### Description
Creates a new account with associated threshold severity settings and tags. Automatically generates RSA key pairs for the account.

### Request Headers
| Header | Type | Required | Description |
|--------|------|----------|-------------|
| Content-Type | String | Yes | application/json |

### Request Body
```json
{
    "identifier": "new-account-001",
    "accountName": "New Production Account",
    "closingWindow": "24h",
    "maxDataBreaks": "5",
    "status": 1,
    "thresholdSeverityBean": {
        "low": true,
        "warning": true,
        "critical": false
    },
    "tag": [
        {
            "identifier": "env-tag",
            "name": "Environment"
        },
        {
            "identifier": "region-tag",
            "name": "Region"
        }
    ]
}
```

### Response Structure
```json
{
    "identifier": "new-account-001",
    "accountName": "New Production Account",
    "closingWindow": "24h",
    "maxDataBreaks": "5",
    "status": 1,
    "thresholdSeverityBean": {
        "id": 123,
        "low": true,
        "warning": true,
        "critical": false
    },
    "tag": [
        {
            "id": 456,
            "identifier": "env-tag",
            "name": "Environment"
        },
        {
            "id": 457,
            "identifier": "region-tag",
            "name": "Region"
        }
    ]
}
```

### Business Logic Flow
1. **Key Generation**: Generate RSA key pair using KeyGenerator utility
2. **Account Creation**: Insert account record into `account_1` table
3. **Threshold Severity**: Insert threshold settings into `threshold_severity` table (if provided)
5. **Response Building**: Fetch created account with all associations and build response

### Error Scenarios
- **400 Bad Request**: Invalid request body or validation failures
- **409 Conflict**: Account identifier already exists
- **500 Internal Server Error**: Database errors or key generation failures

## Data Models

### AccountRequest (Input)
```java
public class AccountRequest {
    private String identifier;           // Required, unique
    private String accountName;          // Required
    private String closingWindow;        // Optional
    private String maxDataBreaks;        // Optional
    private int status;                  // Required, default 1
    private ThresholdSeverityBean thresholdSeverityBean; // Optional
    private List<Tags> tag;           // Optional
}
```

### AccountResponse (Output)
```java
public class AccountResponse {
    private String identifier;
    private String accountName;
    private String closingWindow;
    private String maxDataBreaks;
    private int status;
    private ThresholdSeverityBean thresholdSeverityBean;
    private List<Tags> tag;
}
```

### ThresholdSeverityBean
```java
public class ThresholdSeverityBean {
    private Integer id;                  // Auto-generated
    private boolean low;                 // Default false
    private boolean warning;             // Default false
    private boolean critical;            // Default false
}
```

### Tags 
```java
public class Tags {
    private Integer id;                  // Auto-generated
    private String name;                 // Required
    private String identifier;           // Required
    private String value;
}
```
### Account (GET Response)
```java
public class Account {
    private Integer accountId;
    private String accountName;
    private int status;
    private String privateKey;           // Sensitive data
    private String publicKey;
    private List<Tag> tags;
    private long timezoneMilli;
    private String timeZoneString;
    private Long updatedTime;
    private String updatedBy;
    private String dateFormat;
    private String timeFormat;
    private String identifier;
}
```

## Error Handling

### Error Response Structure
All error responses follow a consistent structure:

```json
{
    "message": "Error description",
    "data": {
        "timestamp": "2024-01-15T10:30:00Z",
        "status": 400,
        "error": ["Detailed error messages"],
        "type": "ClientException",
        "path": "uri=/v2.0/api/accounts",
        "message": "Validation failed"
    },
    "responseStatus": "BAD_REQUEST"
}
```

### HTTP Status Codes

#### GET /accounts
| Status Code | Description | Scenario |
|-------------|-------------|----------|
| 200 | OK | Successful retrieval |
| 401 | Unauthorized | Invalid/missing authorization token |
| 500 | Internal Server Error | Database errors, processing failures |

#### POST /accounts
| Status Code | Description | Scenario |
|-------------|-------------|----------|
| 201 | Created | Account successfully created |
| 400 | Bad Request | Invalid request body, validation failures |
| 401 | Unauthorized | Invalid/missing authorization token |
| 409 | Conflict | Account identifier already exists |
| 500 | Internal Server Error | Database errors, key generation failures |

### Exception Types

#### ClientException
- **Trigger**: Invalid input data, missing required fields
- **HTTP Status**: 400 Bad Request
- **Example**: Missing account identifier, invalid authorization token

#### ServerException
- **Trigger**: Server-side validation failures
- **HTTP Status**: 500 Internal Server Error
- **Example**: User access validation failures

#### DataProcessingException
- **Trigger**: Data processing or transformation errors
- **HTTP Status**: 500 Internal Server Error
- **Example**: Database query failures, data mapping errors

#### ControlCenterException
- **Trigger**: General application errors
- **HTTP Status**: 500 Internal Server Error
- **Example**: Key generation failures, unexpected system errors

## Security

### Authentication
- **Method**: Bearer Token Authentication
- **Header**: `Authorization: Bearer <token>`
- **Validation**: Token validated against Keycloak SSO
- **User Extraction**: User ID extracted from validated token

### Authorization
- **GET /accounts**: Requires valid user authentication
- **POST /accounts**: Currently no additional authorization checks beyond authentication

### Data Security
- **Key Generation**: RSA key pairs automatically generated for each account
- **Sensitive Data**: Private keys stored securely in database
- **Data Exposure**: Private/public keys included in GET response (consider security implications)

### Security Headers
Standard security headers applied via `JsonFileParser.loadHeaderConfiguration()`:
- `X-Frame-Options: SAMEORIGIN`
- `Cache-Control: no-cache, no-store, must-revalidate`
- `Strict-Transport-Security: max-age=********`
- `Content-Security-Policy: frame-src 'self'; frame-ancestors 'self'; object-src 'none';`

## Validation Rules

### POST /accounts Request Validation

#### Required Fields
- `identifier`: Must be unique, non-null, non-empty
- `accountName`: Must be non-null, non-empty
- `status`: Must be valid integer (typically 1 for active)

#### Optional Fields
- `closingWindow`: String format, application-specific validation
- `maxDataBreaks`: String format, application-specific validation
- `thresholdSeverityBean`: Object with boolean fields
- `tag`: Array of tag objects

#### ThresholdSeverityBean Validation
- `low`: Boolean value (default: false)
- `warning`: Boolean value (default: false)
- `critical`: Boolean value (default: false)

#### TagBean Validation
- `name`: Required, non-null, non-empty
- `identifier`: Required, non-null, non-empty

### GET /accounts Request Validation
- `Authorization`: Required, non-null, non-empty Bearer token
- User ID must be extractable from token

### Business Rules
1. **Account Identifier Uniqueness**: Must be unique across all accounts
2. **Status Values**: Typically 1 (active) or 0 (inactive)
3. **Tag Associations**: Tags are associated with specific account via account_id
4. **Threshold Severity**: One-to-one relationship with account
5. **Key Generation**: Automatic RSA key pair generation for security

## Examples

### GET /accounts - Success Response
```http
GET /v2.0/api/accounts
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...

HTTP/1.1 200 OK
Content-Type: application/json

{
    "message": "Accounts fetched successfully.",
    "data": [
        {
            "accountId": 1,
            "accountName": "Production Environment",
            "status": 1,
            "privateKey": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...",
            "publicKey": "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuGbXWi...",
            "tags": [
                {
                    "name": "Environment",
                    "identifier": "env-prod"
                },
                {
                    "name": "Region",
                    "identifier": "region-us-east"
                }
            ],
            "timezoneMilli": ********,
            "timeZoneString": "Asia/Kolkata",
            "updatedTime": *************,
            "updatedBy": "system",
            "dateFormat": null,
            "timeFormat": null,
            "identifier": "prod-account-001"
        }
    ],
    "responseStatus": "OK"
}
```

### POST /accounts - Success Request/Response
```http
POST /v2.0/api/accounts
Content-Type: application/json

{
    "identifier": "staging-account-002",
    "accountName": "Staging Environment",
    "closingWindow": "12h",
    "maxDataBreaks": "3",
    "status": 1,
    "thresholdSeverityBean": {
        "low": true,
        "warning": true,
        "critical": true
    },
    "tag": [
        {
            "identifier": "env-staging",
            "name": "Environment"
        },
        {
            "identifier": "team-qa",
            "name": "Team"
        }
    ]
}

HTTP/1.1 201 Created
Content-Type: application/json

{
    "identifier": "staging-account-002",
    "accountName": "Staging Environment",
    "closingWindow": "12h",
    "maxDataBreaks": "3",
    "status": 1,
    "thresholdSeverityBean": {
        "id": 45,
        "low": true,
        "warning": true,
        "critical": true
    },
    "tag": [
        {
            "id": 123,
            "identifier": "env-staging",
            "name": "Environment"
        },
        {
            "id": 124,
            "identifier": "team-qa",
            "name": "Team"
        }
    ]
}
```

### Error Response Examples

#### GET /accounts - Unauthorized
```http
GET /v2.0/api/accounts
Authorization: Bearer invalid_token

HTTP/1.1 401 Unauthorized
Content-Type: application/json

{
    "message": "Invalid authorization token",
    "data": {
        "timestamp": "2024-01-15T10:30:00Z",
        "status": 401,
        "error": ["Invalid authorization token"],
        "type": "ClientException",
        "path": "uri=/v2.0/api/accounts",
        "message": "Invalid authorization token"
    },
    "responseStatus": "UNAUTHORIZED"
}
```

#### POST /accounts - Validation Error
```http
POST /v2.0/api/accounts
Content-Type: application/json

{
    "accountName": "Test Account",
    "status": 1
}

HTTP/1.1 400 Bad Request
Content-Type: application/json

{
    "message": "Validation failed",
    "data": {
        "timestamp": "2024-01-15T10:30:00Z",
        "status": 400,
        "error": ["Account identifier is required"],
        "type": "ClientException",
        "path": "uri=/v2.0/api/accounts",
        "message": "Validation failed"
    },
    "responseStatus": "BAD_REQUEST"
}
```

#### POST /accounts - Duplicate Identifier
```http
POST /v2.0/api/accounts
Content-Type: application/json

{
    "identifier": "existing-account",
    "accountName": "Duplicate Account",
    "status": 1
}

HTTP/1.1 409 Conflict
Content-Type: application/json

{
    "message": "Account identifier already exists",
    "data": {
        "timestamp": "2024-01-15T10:30:00Z",
        "status": 409,
        "error": ["Account with identifier 'existing-account' already exists"],
        "type": "ControlCenterException",
        "path": "uri=/v2.0/api/accounts",
        "message": "Account identifier already exists"
    },
    "responseStatus": "CONFLICT"
}
```

## Implementation Notes

### Architecture Components

#### Controller Layer
- **AccountController**: REST endpoint handler
- **Responsibilities**: Request/response handling, HTTP status management
- **Dependencies**: GetAccountsBL, PostAccountService, JsonFileParser

#### Business Logic Layer
- **GetAccountsBL**: Implements BusinessLogic interface for GET operations
- **PostAccountService**: Service class for POST operations
- **Validation**: Client validation, server validation, data processing

#### Data Access Layer
- **AccountsDao**: Database operations for account management
- **Methods**: getAccounts(), insertAccount(), insertThresholdSeverity(), insertTag()

#### Utility Components
- **KeyGenerator**: RSA key pair generation
- **CommonUtils**: User ID extraction from tokens
- **JsonFileParser**: Configuration and header management

### Database Considerations

#### Table Relationships
- `account_1` → `threshold_severity1` (1:1)
- `account_1` → `tag_1` (1:N)
- Legacy `account` table used for GET operations

#### Transaction Management
- POST operations should be transactional
- Rollback on any failure during account creation
- Consider implementing @Transactional annotations

#### Performance Considerations
- GET operations use parallel streams for timezone processing
- Consider pagination for large account lists
- Index on identifier fields for uniqueness checks

### Security Considerations

#### Key Management
- RSA keys generated automatically
- Private keys stored in database (consider encryption at rest)
- Key rotation strategy not currently implemented

#### Data Exposure
- Private keys included in GET response (security risk)
- Consider separate endpoint for key retrieval
- Implement field-level security

#### Authentication Flow
1. Extract Bearer token from Authorization header
2. Validate token against Keycloak SSO
3. Extract user ID from validated token
4. Proceed with business logic

### Error Handling Strategy

#### Exception Hierarchy
```
Exception
├── ControlCenterException (General application errors)
├── ClientException (Client-side validation errors)
├── ServerException (Server-side validation errors)
└── DataProcessingException (Data processing errors)
```

#### Global Exception Handler
- **ExceptionHandler**: Centralized error handling
- **Response Format**: Consistent error response structure
- **Logging**: Comprehensive error logging with request context

### Future Enhancements

#### API Improvements
1. **Pagination**: Add pagination support for GET /accounts
2. **Filtering**: Add query parameters for filtering accounts
3. **Partial Updates**: Implement PATCH endpoint for account updates
4. **Bulk Operations**: Support bulk account creation/updates

#### Security Enhancements
1. **Field-Level Security**: Exclude sensitive data from responses
2. **Role-Based Access**: Implement granular permissions
3. **Key Encryption**: Encrypt private keys at rest
4. **Audit Logging**: Track all account modifications

#### Validation Improvements
1. **Input Validation**: Add comprehensive validation annotations
2. **Business Rules**: Implement complex business rule validation
3. **Async Validation**: Add asynchronous validation for external dependencies

#### Performance Optimizations
1. **Caching**: Implement caching for frequently accessed accounts
2. **Database Optimization**: Optimize queries and indexes
3. **Async Processing**: Consider async processing for heavy operations

### Testing Strategy

#### Unit Tests
- Controller layer testing with MockMvc
- Business logic testing with mocked dependencies
- DAO layer testing with test database

#### Integration Tests
- End-to-end API testing
- Database integration testing
- Security integration testing

#### Test Data Management
- Test data fixtures for consistent testing
- Database cleanup between tests
- Mock external dependencies (Keycloak)

### Monitoring and Observability

#### Logging
- Request/response logging
- Error logging with stack traces
- Performance metrics logging

#### Metrics
- API response times
- Error rates by endpoint
- Database query performance

#### Health Checks
- Database connectivity
- External service dependencies
- Application health endpoints

---

## Conclusion

This design document provides a comprehensive specification for the Accounts API, covering both GET and POST operations. The implementation follows established patterns in the codebase and provides a solid foundation for account management functionality.

Key highlights:
- **Consistent API Design**: Follows existing patterns and conventions
- **Comprehensive Error Handling**: Robust error scenarios and responses
- **Security Considerations**: Authentication, authorization, and data protection
- **Extensible Architecture**: Designed for future enhancements and scalability

The API is ready for implementation following the detailed specifications provided in this document.