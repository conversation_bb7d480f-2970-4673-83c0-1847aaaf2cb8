# Heal Control Center - Accounts API Documentation

This directory contains comprehensive documentation for the Accounts API in the Heal Control Center system.

## 📋 Documentation Overview

### 📄 [accounts-api-design.md](./accounts-api-design.md)
**Comprehensive Design Document**

A detailed technical specification covering:
- **API Overview**: Purpose, scope, and base URL
- **Database Schema**: Table structures and relationships
- **GET /accounts API**: Detailed endpoint specification
- **POST /accounts API**: Account creation specification
- **Data Models**: Complete request/response structures
- **Error Handling**: Comprehensive error scenarios and responses
- **Security**: Authentication, authorization, and data protection
- **Validation Rules**: Input validation requirements
- **Examples**: Real-world request/response examples
- **Implementation Notes**: Architecture, performance, and future enhancements

### 🔧 [accounts-api-openapi.yaml](./accounts-api-openapi.yaml)
**OpenAPI 3.0 Specification**

Machine-readable API specification including:
- Complete endpoint definitions
- Request/response schemas
- Authentication requirements
- Error response structures
- Interactive documentation support

## 🚀 Quick Start

### API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/v2.0/api/accounts` | Retrieve all active accounts |
| `POST` | `/v2.0/api/accounts` | Create a new account |

### Authentication
All endpoints require Bearer token authentication:
```http
Authorization: Bearer <your-jwt-token>
```

### Base URL
```
{server}/v2.0/api
```

## 📊 Key Features

### GET /accounts
- ✅ Retrieve all active accounts
- ✅ Include timezone information
- ✅ Include associated tags
- ✅ Include cryptographic keys
- ✅ Parallel processing for performance

### POST /accounts
- ✅ Create new accounts
- ✅ Automatic RSA key generation
- ✅ Optional threshold severity settings
- ✅ Optional tag associations
- ✅ Transactional operations

## 🔒 Security

- **Authentication**: Keycloak SSO integration
- **Authorization**: Bearer token validation
- **Data Protection**: RSA key pair generation
- **Security Headers**: Comprehensive security headers
- **Input Validation**: Robust validation rules

## 🗄️ Database Schema

### Primary Tables
- `account_1`: Main account storage
- `threshold_severity1`: Severity threshold settings
- `tag_1`: Account tags
- `account`: Legacy table for GET operations

### Relationships
- Account → Threshold Severity (1:1)
- Account → Tags (1:N)

## 📝 Request/Response Examples

### Create Account
```bash
curl -X POST /v2.0/api/accounts \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "prod-account-001",
    "accountName": "Production Environment",
    "status": 1,
    "thresholdSeverityBean": {
      "low": true,
      "warning": true,
      "critical": false
    },
    "tag": [
      {
        "identifier": "env-prod",
        "name": "Environment"
      }
    ]
  }'
```

### Get All Accounts
```bash
curl -X GET /v2.0/api/accounts \
  -H "Authorization: Bearer <token>"
```

## 🛠️ Implementation Status

### ✅ Completed Features
- [x] GET /accounts endpoint
- [x] POST /accounts endpoint
- [x] Authentication integration
- [x] Error handling
- [x] Database operations
- [x] Key generation
- [x] Tag management
- [x] Threshold severity settings

### 🔄 Current Architecture
- **Controller**: `AccountController`
- **Business Logic**: `GetAccountsBL`, `PostAccountService`
- **Data Access**: `AccountsDao`
- **Utilities**: `KeyGenerator`, `CommonUtils`, `JsonFileParser`

## 🚧 Future Enhancements

### API Improvements
- [ ] Pagination support for GET /accounts
- [ ] Query parameters for filtering
- [ ] PATCH endpoint for partial updates
- [ ] Bulk operations support

### Security Enhancements
- [ ] Field-level security for sensitive data
- [ ] Role-based access control
- [ ] Private key encryption at rest
- [ ] Audit logging

### Performance Optimizations
- [ ] Response caching
- [ ] Database query optimization
- [ ] Async processing for heavy operations

## 📖 Usage Guidelines

### For Developers
1. Review the [design document](./accounts-api-design.md) for implementation details
2. Use the [OpenAPI specification](./accounts-api-openapi.yaml) for client generation
3. Follow the established patterns and conventions
4. Implement comprehensive error handling

### For API Consumers
1. Obtain valid JWT token from Keycloak SSO
2. Include Authorization header in all requests
3. Handle error responses appropriately
4. Validate response data structures

### For Testing
1. Use the provided examples as test cases
2. Test all error scenarios
3. Validate security requirements
4. Performance test with realistic data volumes

## 🔍 Validation Rules

### Required Fields (POST)
- `identifier`: Unique account identifier
- `accountName`: Human-readable name
- `status`: Account status (1=active, 0=inactive)

### Optional Fields (POST)
- `closingWindow`: Account closing window
- `maxDataBreaks`: Maximum data breaks allowed
- `thresholdSeverityBean`: Severity threshold settings
- `tag`: Array of associated tags

### Business Rules
- Account identifiers must be unique
- RSA keys are automatically generated
- Tags are associated via account_id
- Threshold severity has 1:1 relationship with account

## 📞 Support

For questions or issues related to the Accounts API:

- **Technical Documentation**: Review this documentation
- **Implementation Issues**: Check existing code patterns
- **API Questions**: Refer to OpenAPI specification
- **Security Concerns**: Follow established security practices

## 📚 Related Documentation

- [Heal Control Center Architecture](../README.md)
- [Database Schema Documentation](../database/README.md)
- [Security Guidelines](../security/README.md)
- [API Testing Guide](../testing/README.md)

---

**Last Updated**: January 2024  
**Version**: 2.0.0  
**Status**: Production Ready
