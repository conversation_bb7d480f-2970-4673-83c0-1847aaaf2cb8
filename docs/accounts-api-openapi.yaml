openapi: 3.0.3
info:
  title: Heal Control Center - Accounts API
  description: |
    API for managing accounts in the Heal Control Center system.

    This API provides functionality to:
    - Retrieve all active accounts with their metadata
    - Create new accounts with threshold severity settings and tags

    All endpoints require Bearer token authentication.
  version: 2.0.0
  contact:
    name: Heal Software
    email: <EMAIL>
  license:
    name: Proprietary

servers:
  - url: https://api.heal.com/v2.0/api
    description: Production server
  - url: https://staging-api.heal.com/v2.0/api
    description: Staging server
  - url: http://localhost:8080/v2.0/api
    description: Local development server

security:
  - bearerAuth: []

paths:
  /accounts:
    get:
      summary: Get all accounts
      description: |
        Retrieves a list of all active accounts with their associated metadata including
        timezone information, tags, and cryptographic keys.
      operationId: getAllAccounts
      tags:
        - Accounts
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Accounts retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountListResponse'
              example:
                message: "Accounts fetched successfully."
                data:
                  - accountId: 1
                    accountName: "Production Environment"
                    status: 1
                    privateKey: "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC..."
                    publicKey: "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuGbXWi..."
                    tags:
                      - name: "Environment"
                        identifier: "env-prod"
                    timezoneMilli: ********
                    timeZoneString: "Asia/Kolkata"
                    updatedTime: *************
                    updatedBy: "system"
                    identifier: "prod-account-001"
                responseStatus: "OK"
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create a new account
      description: |
        Creates a new account with optional threshold severity settings and tags.
        Automatically generates RSA key pairs for the account.
      operationId: createAccount
      tags:
        - Accounts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountRequest'
            example:
              identifier: "staging-account-002"
              accountName: "Staging Environment"
              closingWindow: "12h"
              maxDataBreaks: "3"
              status: 1
              thresholdSeverityBean:
                low: true
                warning: true
                critical: true
              tag:
                - identifier: "env-staging"
                  name: "Environment"
                - identifier: "team-qa"
                  name: "Team"
      responses:
        '201':
          description: Account created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountResponse'
              example:
                identifier: "staging-account-002"
                accountName: "Staging Environment"
                closingWindow: "12h"
                maxDataBreaks: "3"
                status: 1
                thresholdSeverityBean:
                  id: 45
                  low: true
                  warning: true
                  critical: true
                tag:
                  - id: 123
                    identifier: "env-staging"
                    name: "Environment"
                  - id: 124
                    identifier: "team-qa"
                    name: "Team"
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '409':
          $ref: '#/components/responses/ConflictError'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from Keycloak SSO

  schemas:
    AccountListResponse:
      type: object
      properties:
        message:
          type: string
          example: "Accounts fetched successfully."
        data:
          type: array
          items:
            $ref: '#/components/schemas/Account'
        responseStatus:
          type: string
          enum: [OK]

    Account:
      type: object
      properties:
        accountId:
          type: integer
          description: Unique account identifier
          example: 1
        accountName:
          type: string
          description: Human-readable account name
          example: "Production Environment"
        status:
          type: integer
          description: Account status (1=active, 0=inactive)
          example: 1
        privateKey:
          type: string
          description: RSA private key for the account
          format: password
          example: "-----BEGIN PRIVATE KEY-----\n..."
        publicKey:
          type: string
          description: RSA public key for the account
          example: "-----BEGIN PUBLIC KEY-----\n..."
        tags:
          type: array
          items:
            $ref: '#/components/schemas/Tag'
        timezoneMilli:
          type: integer
          format: int64
          description: Timezone offset in milliseconds
          example: ********
        timeZoneString:
          type: string
          description: Timezone identifier
          example: "Asia/Kolkata"
        updatedTime:
          type: integer
          format: int64
          description: Last update timestamp in milliseconds
          example: *************
        updatedBy:
          type: string
          description: User who last updated the account
          example: "system"
        dateFormat:
          type: string
          nullable: true
          description: Preferred date format
        timeFormat:
          type: string
          nullable: true
          description: Preferred time format
        identifier:
          type: string
          description: Unique account identifier string
          example: "prod-account-001"

    AccountRequest:
      type: object
      required:
        - identifier
        - accountName
        - status
      properties:
        identifier:
          type: string
          description: Unique account identifier
          maxLength: 255
          example: "staging-account-002"
        accountName:
          type: string
          description: Human-readable account name
          maxLength: 255
          example: "Staging Environment"
        closingWindow:
          type: string
          description: Account closing window configuration
          example: "12h"
        maxDataBreaks:
          type: string
          description: Maximum allowed data breaks
          example: "3"
        status:
          type: integer
          description: Account status (1=active, 0=inactive)
          enum: [0, 1]
          example: 1
        thresholdSeverityBean:
          $ref: '#/components/schemas/ThresholdSeverityBean'
        tag:
          type: array
          items:
            $ref: '#/components/schemas/TagBean'

    AccountResponse:
      type: object
      properties:
        identifier:
          type: string
          example: "staging-account-002"
        accountName:
          type: string
          example: "Staging Environment"
        closingWindow:
          type: string
          example: "12h"
        maxDataBreaks:
          type: string
          example: "3"
        status:
          type: integer
          example: 1
        thresholdSeverityBean:
          $ref: '#/components/schemas/ThresholdSeverityResponse'
        tag:
          type: array
          items:
            $ref: '#/components/schemas/TagResponse'

    ThresholdSeverityBean:
      type: object
      properties:
        low:
          type: boolean
          description: Enable low severity threshold
          default: false
        warning:
          type: boolean
          description: Enable warning severity threshold
          default: false
        critical:
          type: boolean
          description: Enable critical severity threshold
          default: false

    ThresholdSeverityResponse:
      allOf:
        - $ref: '#/components/schemas/ThresholdSeverityBean'
        - type: object
          properties:
            id:
              type: integer
              description: Database-generated threshold severity ID
              example: 45

    TagBean:
      type: object
      required:
        - identifier
        - name
      properties:
        identifier:
          type: string
          description: Unique tag identifier
          example: "env-staging"
        name:
          type: string
          description: Human-readable tag name
          example: "Environment"

    TagResponse:
      allOf:
        - $ref: '#/components/schemas/TagBean'
        - type: object
          properties:
            id:
              type: integer
              description: Database-generated tag ID
              example: 123

    Tag:
      type: object
      properties:
        name:
          type: string
          description: Tag name
          example: "Environment"
        identifier:
          type: string
          description: Tag identifier
          example: "env-prod"

    ErrorResponse:
      type: object
      properties:
        message:
          type: string
          description: Error message
          example: "Validation failed"
        data:
          $ref: '#/components/schemas/ErrorData'
        responseStatus:
          type: string
          description: HTTP status name
          example: "BAD_REQUEST"

    ErrorData:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
          example: "2024-01-15T10:30:00Z"
        status:
          type: integer
          description: HTTP status code
          example: 400
        error:
          type: array
          items:
            type: string
          description: List of error messages
          example: ["Account identifier is required"]
        type:
          type: string
          description: Exception type
          example: "ClientException"
        path:
          type: string
          description: Request path
          example: "uri=/v2.0/api/accounts"
        message:
          type: string
          description: Error message
          example: "Validation failed"

  responses:
    BadRequestError:
      description: Bad Request - Invalid input data
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            message: "Validation failed"
            data:
              timestamp: "2024-01-15T10:30:00Z"
              status: 400
              error: ["Account identifier is required"]
              type: "ClientException"
              path: "uri=/v2.0/api/accounts"
              message: "Validation failed"
            responseStatus: "BAD_REQUEST"

    UnauthorizedError:
      description: Unauthorized - Invalid or missing authentication token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            message: "Invalid authorization token"
            data:
              timestamp: "2024-01-15T10:30:00Z"
              status: 401
              error: ["Invalid authorization token"]
              type: "ClientException"
              path: "uri=/v2.0/api/accounts"
              message: "Invalid authorization token"
            responseStatus: "UNAUTHORIZED"

    ConflictError:
      description: Conflict - Resource already exists
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            message: "Account identifier already exists"
            data:
              timestamp: "2024-01-15T10:30:00Z"
              status: 409
              error: ["Account with identifier 'existing-account' already exists"]
              type: "ControlCenterException"
              path: "uri=/v2.0/api/accounts"
              message: "Account identifier already exists"
            responseStatus: "CONFLICT"

    InternalServerError:
      description: Internal Server Error - Unexpected server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            message: "Internal server error"
            data:
              timestamp: "2024-01-15T10:30:00Z"
              status: 500
              error: ["Database connection failed"]
              type: "DataProcessingException"
              path: "uri=/v2.0/api/accounts"
              message: "Internal server error"
            responseStatus: "INTERNAL_SERVER_ERROR"
