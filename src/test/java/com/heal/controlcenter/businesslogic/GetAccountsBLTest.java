package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.util.CommonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GetAccountsBLTest {

    @InjectMocks
    private GetAccountsBL getAccountsBL;

    @Mock
    private CommonUtils commonUtils;

    @Mock
    private AccountsDao accountsDao;

    private final String AUTH_TOKEN = "valid-token";
    private final String USER_ID = "user-001";
    private String[] requestParams;

    @BeforeEach
    void setup() {
        requestParams = new String[]{AUTH_TOKEN};
    }

    @Test
    void testClientValidation_success() throws Exception {
        Account mockAccount = new Account();
        when(commonUtils.getUserId(AUTH_TOKEN)).thenReturn(USER_ID);

        UtilityBean<Account> result = getAccountsBL.clientValidation(mockAccount, requestParams);

        assertEquals(AUTH_TOKEN, result.getAuthToken());
        assertEquals(USER_ID, result.getUserId());
    }


    @Test
    void testProcess_success() throws Exception {
        AccountBean accountBean = new AccountBean();
        accountBean.setId(1);
        accountBean.setName("Test Account");
        accountBean.setStatus(1);
        accountBean.setPrivateKey("privateKey");
        accountBean.setPublicKey("publicKey");
        accountBean.setIdentifier("acc-001");
        accountBean.setLastModifiedBy("admin");
        accountBean.setUpdatedTime("2024-01-01 10:00:00");

        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setTimeZoneId("Asia/Kolkata");
        timezoneBean.setOffset(330);

        when(accountsDao.getAccounts()).thenReturn(Collections.singletonList(accountBean));
        when(accountsDao.getAccountTimezoneDetails(accountBean.getId())).thenReturn(timezoneBean);

        List<Account> result = getAccountsBL.process(null);
        assertEquals(1, result.size());

        Account account = result.get(0);
        assertEquals("Test Account", account.getAccountName());
        assertEquals("Asia/Kolkata", account.getTimeZoneString());
        assertEquals(330L * 60 * 1000, account.getTimezoneMilli());
    }

    @Test
    void testProcess_timeZoneNotFound() throws Exception {
        AccountBean accountBean = new AccountBean();
        accountBean.setId(1);
        accountBean.setName("No Timezone Account");
        accountBean.setUpdatedTime("2024-01-01 10:00:00");

        when(accountsDao.getAccounts()).thenReturn(Collections.singletonList(accountBean));
        when(accountsDao.getAccountTimezoneDetails(accountBean.getId())).thenThrow(new ControlCenterException("No timezone"));

        List<Account> result = getAccountsBL.process(null);

        assertEquals(1, result.size());
        assertEquals("UTC", result.get(0).getTimeZoneString());
        assertEquals(0L, result.get(0).getTimezoneMilli());
    }

    @Test
    void testProcess_failure() throws ControlCenterException {
        when(accountsDao.getAccounts()).thenThrow(new RuntimeException("DB failure"));

        Exception exception = assertThrows(ControlCenterException.class, () -> getAccountsBL.process(null));
        assertEquals("Failed to process accounts", exception.getMessage());
    }
}
