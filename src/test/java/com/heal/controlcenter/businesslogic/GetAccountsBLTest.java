package com.heal.controlcenter.businesslogic;

import com.google.gson.Gson;
import com.heal.controlcenter.beans.AccessDetailsBean;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.beans.UserAccessBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.UIMessages;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GetAccountsBLTest {

    @InjectMocks
    private GetAccountsBL getAccountsBL;

    @Mock
    private CommonUtils commonUtils;

    @Mock
    private AccountsDao accountsDao;

    @Mock
    private AccountRepo accountRepo;

    private final String AUTH_TOKEN = "valid-token";
    private final String USER_ID = "user-001";
    private String[] requestParams;

    @BeforeEach
    void setup() {
        requestParams = new String[]{AUTH_TOKEN};
    }

    @Test
    void testClientValidation_success() throws Exception {
        Account mockAccount = new Account();
        UtilityBean<Account> result = getAccountsBL.clientValidation(mockAccount, requestParams);
        assertEquals(AUTH_TOKEN, result.getAuthToken());
    }

    @Test
    void testClientValidation_authTokenNull_throwsClientException() {
        Account mockAccount = new Account();
        String[] emptyParams = new String[]{""};
        ClientException exception = assertThrows(ClientException.class,
                () -> getAccountsBL.clientValidation(mockAccount, emptyParams));
        assertEquals("ClientException : " + UIMessages.AUTH_KEY_EXCEPTION_MESSAGE, exception.getMessage());
    }

    @Test
    void testServerValidation_success() throws Exception {
        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder().authToken(AUTH_TOKEN).build();

        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), any());
        String jsonAccessDetails = new Gson().toJson(accessDetailsBean);

        UserAccessBean userAccessBean = new UserAccessBean();
        userAccessBean.setAccessDetails(jsonAccessDetails);

        when(commonUtils.getUserId(AUTH_TOKEN)).thenReturn(USER_ID);
        when(accountsDao.fetchUserAccessDetailsUsingIdentifier(USER_ID)).thenReturn(userAccessBean);

        AccessDetailsBean result = getAccountsBL.serverValidation(utilityBean);

        assertNotNull(result);
        assertEquals(1, result.getAccounts().size());
        assertEquals("*", result.getAccounts().get(0));
    }

    @Test
    void testServerValidation_userIdExtractionFailure_throwsServerException() throws Exception {
        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder().authToken(AUTH_TOKEN).build();

        when(commonUtils.getUserId(AUTH_TOKEN)).thenThrow(new ControlCenterException("Invalid token"));

        ServerException exception = assertThrows(ServerException.class,
                () -> getAccountsBL.serverValidation(utilityBean));

        assertTrue(exception.getMessage().contains("Error while extracting user identifier"));
    }

    @Test
    void testServerValidation_invalidAccessDetails_throwsServerException() throws Exception {
        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder().authToken(AUTH_TOKEN).build();

        UserAccessBean accessBean = new UserAccessBean();
        accessBean.setAccessDetails("null");

        when(commonUtils.getUserId(AUTH_TOKEN)).thenReturn(USER_ID);
        when(accountsDao.fetchUserAccessDetailsUsingIdentifier(USER_ID)).thenReturn(accessBean);
        String expectedMessage = "ServerException : access details is not available";
        ServerException exception = assertThrows(ServerException.class,
                () -> getAccountsBL.serverValidation(utilityBean));

        assertEquals(expectedMessage, exception.getMessage());
    }

    @Test
    void testProcess_success_withWildcardAccess() throws Exception {
        com.heal.configuration.pojos.Account accountBean = new com.heal.configuration.pojos.Account();
        accountBean.setId(1);
        accountBean.setName("Test Account");
        accountBean.setStatus(1);
        accountBean.setPrivateKey("privateKey");
        accountBean.setPublicKey("publicKey");
        accountBean.setIdentifier("acc-001");
        accountBean.setLastModifiedBy("admin");
        accountBean.setUpdatedTime("2024-01-01 10:00:00");

        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setTimeZoneId("Asia/Kolkata");
        timezoneBean.setOffset(330);

        AccessDetailsBean accessDetailsBean = new AccessDetailsBean();
        accessDetailsBean.setAccounts(List.of("*")); // allow all accounts

        when(accountRepo.getAccounts()).thenReturn(List.of(accountBean));
        when(accountsDao.getAccountTimezoneDetails(accountBean.getId())).thenReturn(timezoneBean);

        List<Account> result = getAccountsBL.process(accessDetailsBean);

        assertNotNull(result);
        assertEquals(1, result.size());

        Account account = result.get(0);
        assertEquals("Test Account", account.getAccountName());
        assertEquals("Asia/Kolkata", account.getTimeZoneString());
        assertEquals(330L * 60 * 1000, account.getTimezoneMilli());
    }

    @Test
    void testProcess_withRestrictedAccess_filtersAccounts() throws Exception {
        com.heal.configuration.pojos.Account accountBean1 = new com.heal.configuration.pojos.Account();
        accountBean1.setId(1);
        accountBean1.setName("Allowed");
        accountBean1.setIdentifier("acc-allowed");
        accountBean1.setUpdatedTime("2024-01-01 10:00:00");

        com.heal.configuration.pojos.Account accountBean2 = new com.heal.configuration.pojos.Account();
        accountBean2.setId(2);
        accountBean2.setName("Blocked");
        accountBean2.setIdentifier("acc-blocked");
        accountBean2.setUpdatedTime("2024-01-01 10:00:00");

        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setTimeZoneId("UTC");
        timezoneBean.setOffset(0);

        AccessDetailsBean accessDetailsBean = new AccessDetailsBean();
        accessDetailsBean.setAccounts(List.of("acc-allowed"));

        when(accountRepo.getAccounts()).thenReturn(List.of(accountBean1, accountBean2));
        when(accountsDao.getAccountTimezoneDetails(1)).thenReturn(timezoneBean);

        List<Account> result = getAccountsBL.process(accessDetailsBean);

        assertEquals(1, result.size());
        assertEquals("Allowed", result.get(0).getAccountName());
    }

    @Test
    void testProcess_timezoneFetchFailure_throwsException() throws Exception {
        com.heal.configuration.pojos.Account accountBean = new com.heal.configuration.pojos.Account();
        accountBean.setId(1);
        accountBean.setIdentifier("acc-001");
        accountBean.setUpdatedTime("2024-01-01 10:00:00");

        AccessDetailsBean accessDetailsBean = new AccessDetailsBean();
        accessDetailsBean.setAccounts(List.of("*"));

        when(accountRepo.getAccounts()).thenReturn(List.of(accountBean));
        when(accountsDao.getAccountTimezoneDetails(accountBean.getId()))
                .thenThrow(new ControlCenterException("DB Error"));

        assertThrows(DataProcessingException.class, () -> getAccountsBL.process(accessDetailsBean));
    }
}
