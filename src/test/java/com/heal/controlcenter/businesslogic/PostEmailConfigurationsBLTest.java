package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.SMTPDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMTPDetailsPojo;
import com.heal.controlcenter.util.AECSBouncyCastleUtil;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.Timestamp;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({PostEmailConfigurationsBL.class})
class PostEmailConfigurationsBLTest {

    @InjectMocks
    PostEmailConfigurationsBL postEmailConfigurationsBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    NotificationsDao notificationsDao;
    @Mock
    AECSBouncyCastleUtil aecsBouncyCastleUtil;
    @Mock
    MasterDataDao masterDataDao;
    @Mock
    DateTimeUtil dateTimeUtil;

    String[] requestParams = new String[2];
    UtilityBean<SMTPDetailsPojo> mockUtilityBean = null;
    SMTPDetailsPojo smtpDetailsPojo = new SMTPDetailsPojo();
    SMTPDetailsBean smtpDetailsBean = new SMTPDetailsBean();
    AccountBean account = new AccountBean();
    ViewTypesBean securityType = new ViewTypesBean();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        account.setId(2);

        smtpDetailsPojo.setSecurity("SSL");
        smtpDetailsPojo.setAddress("trial");
        smtpDetailsPojo.setPort(8996);
        smtpDetailsPojo.setFromRecipient("<EMAIL>");
        smtpDetailsPojo.setUsername("heal");
        smtpDetailsPojo.setPassword("password");

        smtpDetailsBean.setSecurityId(152);
        smtpDetailsBean.setAddress("trial");
        smtpDetailsBean.setPort(8996);
        smtpDetailsBean.setFromRecipient("<EMAIL>");
        smtpDetailsBean.setUsername("heal");
        smtpDetailsBean.setPassword("password");
        smtpDetailsBean.setAccountId(2);
        smtpDetailsBean.setLastModifiedBy("mockUserId");
        smtpDetailsBean.setCreatedTime("2021-12-02 02:02:02");
        smtpDetailsBean.setUpdatedTime("2021-12-02 02:02:02");
        smtpDetailsBean.setStatus(1);

        mockUtilityBean = UtilityBean.<SMTPDetailsPojo>builder()
                .userId("mockUserId")
                .authToken("mockAuthToken")
                .accountIdentifier("mockAccountIdentifier")
                .pojoObject(smtpDetailsPojo)
                .account(account)
                .build();

        securityType.setTypeName("SMTP Security");
        securityType.setSubTypeId(152);
        securityType.setTypeId(49);
        securityType.setSubTypeName("SSL");
        ReflectionTestUtils.setField(postEmailConfigurationsBL, "securityType", securityType);
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
        mockUtilityBean = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                postEmailConfigurationsBL.clientValidation(smtpDetailsPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                postEmailConfigurationsBL.clientValidation(smtpDetailsPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                postEmailConfigurationsBL.clientValidation(smtpDetailsPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_InvalidRequestBody() throws Exception {
        String expectedMessage = "ClientException : {SMTP address=Address is either NULL, empty or its length is greater than 256 characters.}";
        smtpDetailsPojo.setAddress("");

        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                postEmailConfigurationsBL.clientValidation(smtpDetailsPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<SMTPDetailsPojo> utilityBean = postEmailConfigurationsBL.clientValidation(smtpDetailsPojo, requestParams);
        assertEquals(utilityBean.getUserId(), userId);
    }

    @Test
    void serverValidation_Failure_SettingsAlreadyPresent() {
        String expectedMessage = "ServerException : SMTP settings already present.";
        AccountBean account = new AccountBean();
        account.setId(1);

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        when(notificationsDao.getSMTPDetails(account.getId())).thenReturn(smtpDetailsBean);
        ServerException requestException = assertThrows(ServerException.class, () ->
                postEmailConfigurationsBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                postEmailConfigurationsBL.serverValidation(mockUtilityBean));
        System.out.println(requestException.getMessage());
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void serverValidation_Success() throws Exception {
        String userId = "mockUserId";

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        when(masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SMTP_PROTOCOLS, mockUtilityBean.getPojoObject().getSecurity())).thenReturn(securityType);
        when(aecsBouncyCastleUtil.decrypt(mockUtilityBean.getPojoObject().getPassword())).thenReturn("password");
        UtilityBean<SMTPDetailsPojo> utilityBean = postEmailConfigurationsBL.serverValidation(mockUtilityBean);
        assertEquals(utilityBean.getUserId(), userId);
    }

    @Test
    void process_Success() throws Exception {
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));
        postEmailConfigurationsBL.process(mockUtilityBean);
    }

    @Test
    void process_Failure() throws Exception {
        String expectedMessage = "DataProcessingException : Error occurred while adding SMTP details for account.";
        ControlCenterException controlCenterException = new ControlCenterException("Error occurred while adding SMTP details for account.");
        when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.valueOf("2021-12-02 02:02:02"));
        doThrow(controlCenterException).when(notificationsDao).addSMTPDetails(smtpDetailsBean);
        DataProcessingException requestException = assertThrows(DataProcessingException.class, () ->
                postEmailConfigurationsBL.process(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }
}
