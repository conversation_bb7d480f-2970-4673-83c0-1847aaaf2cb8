package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.AutoDiscoveryDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.enums.Entity;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AutoDiscoveryMapEntityPojo;
import com.heal.controlcenter.util.CommonUtils;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;

import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({PostAutoDiscoveryMapToServiceBL.class})
class PostAutoDiscoveryMapToServiceBLTest {

    @InjectMocks
    PostAutoDiscoveryMapToServiceBL postAutoDiscoveryMapToServiceBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    AutoDiscoveryDao autoDiscoveryDao;
    @Mock
    ControllerDao controllerDao;

    String[] requestParams = new String[2];
    UtilityBean<AutoDiscoveryMapEntityPojo> mockUtilityBean = null;
    AutoDiscoveryMapEntityPojo autoDiscoveryMapEntityPojo = new AutoDiscoveryMapEntityPojo();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        autoDiscoveryMapEntityPojo.setEntityType(Entity.Host);

        String[] entityIdentifiers = new String[1];
        entityIdentifiers[0] = "MOCK_IDENTIFIER";
        autoDiscoveryMapEntityPojo.setServiceMappingIdentifiers(entityIdentifiers);

        int[] serviceIds = new int[1];
        serviceIds[0] = 1;
        autoDiscoveryMapEntityPojo.setServiceIdentifiers(serviceIds);

        mockUtilityBean = UtilityBean.<AutoDiscoveryMapEntityPojo>builder()
                .userId("mockUserId")
                .authToken("mockAuthToken")
                .accountIdentifier("mockAccountIdentifier")
                .pojoObject(autoDiscoveryMapEntityPojo)
                .build();
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
        mockUtilityBean = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                postAutoDiscoveryMapToServiceBL.clientValidation(autoDiscoveryMapEntityPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                postAutoDiscoveryMapToServiceBL.clientValidation(autoDiscoveryMapEntityPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                postAutoDiscoveryMapToServiceBL.clientValidation(autoDiscoveryMapEntityPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_InvalidUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                postAutoDiscoveryMapToServiceBL.clientValidation(autoDiscoveryMapEntityPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Disabled
    @Test
    void clientValidations_InvalidRequestBody() throws Exception {
        String expectedMessage = "ClientException : {entityType=Entity type cannot be null in request.}";
        autoDiscoveryMapEntityPojo.setEntityType(null);

        when(commonUtils.getUserId("mockAuthToken")).thenReturn("mockUserId");
        ClientException requestException = assertThrows(ClientException.class, () ->
                postAutoDiscoveryMapToServiceBL.clientValidation(autoDiscoveryMapEntityPojo, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<AutoDiscoveryMapEntityPojo> utilityBean = postAutoDiscoveryMapToServiceBL.clientValidation(autoDiscoveryMapEntityPojo, requestParams);
        assertEquals(utilityBean.getUserId(), userId);
    }

    @Test
    void serverValidation_Success() throws Exception {
        AccountBean account = new AccountBean();
        account.setId(1);

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        AutoDiscoveryMapEntityPojo pojo = postAutoDiscoveryMapToServiceBL.serverValidation(mockUtilityBean);
        assertEquals(account.getId(), pojo.getAccountId());
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                postAutoDiscoveryMapToServiceBL.serverValidation(mockUtilityBean));
        System.out.println(requestException.getMessage());
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    /*@Test
    void process_Failure_UNMAPPING_InvalidEntities() throws Exception {
        List<AutoDiscoveryHostBean> hostsList = new ArrayList<>();
        AutoDiscoveryHostBean autoDiscoveryHostBean = new AutoDiscoveryHostBean();
        autoDiscoveryHostBean.setHostIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        autoDiscoveryHostBean.setHostname("CentOS7");
        hostsList.add(autoDiscoveryHostBean);

        when(autoDiscoveryDao.getHostList(any())).thenReturn(hostsList);
        ControllerBean bean = new ControllerBean();
        bean.setId(1);
        List<ControllerBean> beans = new ArrayList<>();
        beans.add(bean);
        when(controllerDao.getControllerList(0)).thenReturn(beans);
        List<String> data = postAutoDiscoveryMapToServiceBL.process(autoDiscoveryMapEntityPojo);
        assertEquals(2, data.size());
        assertTrue(data.contains("UNMAPPING"));
    }*/

    @Test
    void process_Success_MAPPING() throws Exception {
        int[] serviceIdentifiers = new int[1];
        serviceIdentifiers[0] = 4;
        autoDiscoveryMapEntityPojo.setServiceIdentifiers(serviceIdentifiers);
        String[] entityIdentifiers = autoDiscoveryMapEntityPojo.getServiceMappingIdentifiers();
        entityIdentifiers[0] = "4D971B24CD8EBDD100F75E72F3660C48";

        List<AutoDiscoveryHostBean> hostsList = new ArrayList<>();
        List<AutoDiscoveryProcessBean> processList = new ArrayList<>();
        List<ControllerBean> controllerList = new ArrayList<>();

        AutoDiscoveryHostBean autoDiscoveryHostBean = new AutoDiscoveryHostBean();
        autoDiscoveryHostBean.setHostIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        autoDiscoveryHostBean.setHostname("CentOS7");
        autoDiscoveryHostBean.setDiscoveryStatus(DiscoveryStatus.DISCOVERED_NOT_ADDED_TO_SYSTEM);
        hostsList.add(autoDiscoveryHostBean);
        AutoDiscoveryProcessBean autoDiscoveryProcessBean = new AutoDiscoveryProcessBean();
        autoDiscoveryProcessBean.setProcessIdentifier("02E087750B9079C8D93D86897A55AD07");
        autoDiscoveryProcessBean.setHostIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        autoDiscoveryProcessBean.setProcessName("JBoss_192.168.14.233_8445");
        processList.add(autoDiscoveryProcessBean);
        ControllerBean controllerBean = new ControllerBean();
        controllerBean.setId(4);
        controllerList.add(controllerBean);

        when(autoDiscoveryDao.getHostList(any())).thenReturn(hostsList);
        when(autoDiscoveryDao.getProcessList()).thenReturn(processList);
        when(controllerDao.getControllerList(anyInt())).thenReturn(controllerList);

        List<String> data = postAutoDiscoveryMapToServiceBL.process(autoDiscoveryMapEntityPojo);
        assertEquals(1, data.size());
        assertTrue(data.contains("PASS"));
    }

    /*@Test
    void process_Success_UNMAPPING() throws Exception {
        autoDiscoveryMapEntityPojo.setServiceIdentifiers(null);
        String[] entityIdentifiers = autoDiscoveryMapEntityPojo.getServiceMappingIdentifiers();
        entityIdentifiers[0] = "4D971B24CD8EBDD100F75E72F3660C48";

        List<AutoDiscoveryHostBean> hostsList = new ArrayList<>();
        List<AutoDiscoveryServiceMappingBean> serviceMappingBeanList = new ArrayList<>();

        AutoDiscoveryHostBean autoDiscoveryHostBean = new AutoDiscoveryHostBean();
        autoDiscoveryHostBean.setHostIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        autoDiscoveryHostBean.setHostname("CentOS7");
        hostsList.add(autoDiscoveryHostBean);

        AutoDiscoveryServiceMappingBean serviceMappingBean = new AutoDiscoveryServiceMappingBean();
        serviceMappingBean.setServiceMappingIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        serviceMappingBean.setServiceIdentifier("Mock_Service_Identifiers");
        serviceMappingBean.setEntityType(Entity.Host);
        serviceMappingBeanList.add(serviceMappingBean);

        when(autoDiscoveryDao.getHostList(any())).thenReturn(hostsList);
        when(autoDiscoveryDao.getServiceMappingList()).thenReturn(serviceMappingBeanList);

        List<String> data = postAutoDiscoveryMapToServiceBL.process(autoDiscoveryMapEntityPojo);
        assertEquals(1, data.size());
        assertTrue(data.contains("UNMAPPING"));
    }*/
}
