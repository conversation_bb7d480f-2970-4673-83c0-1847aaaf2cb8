package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.AutoDiscoveryDao;
import com.heal.controlcenter.enums.Entity;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AutoDiscoveryIgnoredEntitiesPojo;
import com.heal.controlcenter.util.CommonUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;

import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({GetAutoDiscoveryIgnoredEntitiesBL.class})
class GetAutoDiscoveryIgnoredEntitiesBLTest {

    @Autowired
    @InjectMocks
    GetAutoDiscoveryIgnoredEntitiesBL getAutoDiscoveryIgnoredEntitiesBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    AutoDiscoveryDao autoDiscoveryDao;

    String[] requestParams = new String[2];
    UtilityBean<Object> mockUtilityBean = null;

    List<AutoDiscoveryHostBean> ignoredHostsList = new ArrayList<>();
    List<NetworkInterfaceBean> networkInterfaceList = new ArrayList<>();
    List<AutoDiscoveryDiscoveredAttributesBean> discoveredAttributesList = new ArrayList<>();
    List<AutoDiscoveryProcessBean> ignoredProcessesList = new ArrayList<>();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";
        mockUtilityBean = UtilityBean.builder()
                .userId("mockUserId")
                .authToken("mockAuthToken")
                .accountIdentifier("mockAccountIdentifier")
                .build();
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
        ignoredHostsList = null;
        networkInterfaceList = null;
        discoveredAttributesList = null;
        ignoredProcessesList = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAutoDiscoveryIgnoredEntitiesBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAutoDiscoveryIgnoredEntitiesBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAutoDiscoveryIgnoredEntitiesBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_AuthKeyInvalid() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAutoDiscoveryIgnoredEntitiesBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<Object> utilityBean = getAutoDiscoveryIgnoredEntitiesBL.clientValidation(null, requestParams);
        assertEquals(utilityBean.getUserId(), userId);
    }

    @Test
    void serverValidation_Success() throws Exception {
        AccountBean account = new AccountBean();
        account.setId(1);

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        getAutoDiscoveryIgnoredEntitiesBL.serverValidation(mockUtilityBean);
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                getAutoDiscoveryIgnoredEntitiesBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Success() throws Exception {
        AutoDiscoveryHostBean autoDiscoveryHostBean = new AutoDiscoveryHostBean();
        autoDiscoveryHostBean.setHostname("CentOS7");
        autoDiscoveryHostBean.setHostIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        autoDiscoveryHostBean.setLastDiscoveryRunTime("*************");
        ignoredHostsList.add(autoDiscoveryHostBean);

        NetworkInterfaceBean autoDiscoveryNetworkInterfaceBean = new NetworkInterfaceBean();
        autoDiscoveryNetworkInterfaceBean.setNetworkInterfaceIdentifier("A8089FA70C04837535087D37010A09E0");
        autoDiscoveryNetworkInterfaceBean.setHostIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        autoDiscoveryNetworkInterfaceBean.setInterfaceIP("**************");
        networkInterfaceList.add(autoDiscoveryNetworkInterfaceBean);

        AutoDiscoveryProcessBean autoDiscoveryProcessBean = new AutoDiscoveryProcessBean();
        autoDiscoveryProcessBean.setProcessIdentifier("02E087750B9079C8D93D86897A55AD07");
        autoDiscoveryProcessBean.setHostIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        autoDiscoveryProcessBean.setProcessName("JBoss_**************_8445");
        autoDiscoveryProcessBean.setLastDiscoveryRunTime("*************");
        ignoredProcessesList.add(autoDiscoveryProcessBean);

        AutoDiscoveryDiscoveredAttributesBean autoDiscoveryDiscoveredAttributesBean = new AutoDiscoveryDiscoveredAttributesBean();
        autoDiscoveryDiscoveredAttributesBean.setId(18);
        autoDiscoveryDiscoveredAttributesBean.setDiscoveredAttributesIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        autoDiscoveryDiscoveredAttributesBean.setEntityType(Entity.Host);
        autoDiscoveryDiscoveredAttributesBean.setAttributeName("SshPort");
        autoDiscoveryDiscoveredAttributesBean.setAttributeValue("22");
        discoveredAttributesList.add(autoDiscoveryDiscoveredAttributesBean);

        autoDiscoveryDiscoveredAttributesBean = new AutoDiscoveryDiscoveredAttributesBean();
        autoDiscoveryDiscoveredAttributesBean.setId(18);
        autoDiscoveryDiscoveredAttributesBean.setDiscoveredAttributesIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        autoDiscoveryDiscoveredAttributesBean.setEntityType(Entity.Host);
        autoDiscoveryDiscoveredAttributesBean.setAttributeName("MonitorPort");
        autoDiscoveryDiscoveredAttributesBean.setAttributeValue("22");
        discoveredAttributesList.add(autoDiscoveryDiscoveredAttributesBean);

        autoDiscoveryDiscoveredAttributesBean = new AutoDiscoveryDiscoveredAttributesBean();
        autoDiscoveryDiscoveredAttributesBean.setId(18);
        autoDiscoveryDiscoveredAttributesBean.setDiscoveredAttributesIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        autoDiscoveryDiscoveredAttributesBean.setEntityType(Entity.Host);
        autoDiscoveryDiscoveredAttributesBean.setAttributeName("JMXPort");
        autoDiscoveryDiscoveredAttributesBean.setAttributeValue("22");
        discoveredAttributesList.add(autoDiscoveryDiscoveredAttributesBean);

        autoDiscoveryDiscoveredAttributesBean = new AutoDiscoveryDiscoveredAttributesBean();
        autoDiscoveryDiscoveredAttributesBean.setId(18);
        autoDiscoveryDiscoveredAttributesBean.setDiscoveredAttributesIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        autoDiscoveryDiscoveredAttributesBean.setEntityType(Entity.Host);
        autoDiscoveryDiscoveredAttributesBean.setAttributeName("SomeOtherPort");
        autoDiscoveryDiscoveredAttributesBean.setAttributeValue("22");
        discoveredAttributesList.add(autoDiscoveryDiscoveredAttributesBean);

        autoDiscoveryDiscoveredAttributesBean = new AutoDiscoveryDiscoveredAttributesBean();
        autoDiscoveryDiscoveredAttributesBean.setId(18);
        autoDiscoveryDiscoveredAttributesBean.setDiscoveredAttributesIdentifier("4D971B24CD8EBDD100F75E72F3660C48");
        autoDiscoveryDiscoveredAttributesBean.setEntityType(Entity.CompInstance);
        autoDiscoveryDiscoveredAttributesBean.setAttributeName("SomeOtherAttr");
        autoDiscoveryDiscoveredAttributesBean.setAttributeValue("22");
        discoveredAttributesList.add(autoDiscoveryDiscoveredAttributesBean);

        when(autoDiscoveryDao.getIgnoredHosts()).thenReturn(ignoredHostsList);
        when(autoDiscoveryDao.getNetworkInterfacesList()).thenReturn(networkInterfaceList);
        when(autoDiscoveryDao.getDiscoveredAttributesList()).thenReturn(discoveredAttributesList);
        when(autoDiscoveryDao.getIgnoredProcesses()).thenReturn(ignoredProcessesList);

        List<AutoDiscoveryIgnoredEntitiesPojo> ignoredEntitiesList = getAutoDiscoveryIgnoredEntitiesBL.process(new Object());
        assertEquals(ignoredEntitiesList.size(), 2);
        assertEquals(ignoredEntitiesList.get(0).getName(), "CentOS7");
        assertTrue(ignoredEntitiesList.get(0).getHostAddress().contains("**************"));
        assertEquals(ignoredEntitiesList.get(0).getDiscoveredEntities().size(), 3);
    }
}
