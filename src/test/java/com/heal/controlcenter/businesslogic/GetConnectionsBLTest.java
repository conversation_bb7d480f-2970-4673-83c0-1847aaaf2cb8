package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.GetConnectionPojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.DateTimeUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({GetConnectionsBL.class})
class GetConnectionsBLTest {

    @Autowired
    @InjectMocks
    GetConnectionsBL getConnectionsBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    ControllerDao controllerDao;
    @Mock
    ConnectionDetailsDao connectionDetailsDao;
    @Mock
    AutoDiscoveryDao autoDiscoveryDao;
    @Mock
    DateTimeUtil dateTimeUtil;

    String[] requestParams = new String[2];
    UtilityBean<Object> mockUtilityBean = null;
    List<ControllerBean> controllerBeanList = new ArrayList<>();
    List<ConnectionDetailsBean> healConnectionDetailsBean = new ArrayList<>();
    List<AutoDiscoveryDiscoveredConnectionsBean> discoveredConnectionsBeanList = new ArrayList<>();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        mockUtilityBean = UtilityBean.builder()
                .userId("mockUserId")
                .authToken("mockAuthToken")
                .accountIdentifier("mockAccountIdentifier")
                .build();

        ControllerBean controllerBean = new ControllerBean();
        controllerBean.setId(2);
        controllerBean.setIdentifier("NB-Web-Service-DR");
        controllerBean.setName("NB-Web-Service-DR");
        controllerBean.setStatus(1);
        controllerBean.setControllerTypeId(192);
        controllerBeanList.add(controllerBean);
        controllerBean.setId(3);
        controllerBean.setIdentifier("NB-DB-Service-DR");
        controllerBean.setName("NB-DB-Service-DR");
        controllerBean.setStatus(1);
        controllerBean.setControllerTypeId(192);
        controllerBeanList.add(controllerBean);

        ConnectionDetailsBean connectionDetailsBean = new ConnectionDetailsBean();
        connectionDetailsBean.setId(1);
        connectionDetailsBean.setSourceId(3);
        connectionDetailsBean.setDestinationId(2);
        connectionDetailsBean.setUpdatedTime("2021-06-15 23:28:01");
        healConnectionDetailsBean.add(connectionDetailsBean);

        AutoDiscoveryDiscoveredConnectionsBean autoDiscoveryDiscoveredConnectionsBean = new AutoDiscoveryDiscoveredConnectionsBean();
        autoDiscoveryDiscoveredConnectionsBean.setSourceIdentifier("e3709c5a-2080-40c1-8fca-d1df77ea7b01");
        autoDiscoveryDiscoveredConnectionsBean.setDestinationIdentifier("2a3e219d-1837-4340-b349-46801d47a49c");
        autoDiscoveryDiscoveredConnectionsBean.setDiscoveryStatus(DiscoveryStatus.DISCOVERED_NOT_ADDED_TO_SYSTEM);
        autoDiscoveryDiscoveredConnectionsBean.setLastUpdatedTime(1635483712317L);
        discoveredConnectionsBeanList.add(autoDiscoveryDiscoveredConnectionsBean);
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getConnectionsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getConnectionsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                getConnectionsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_AuthKeyInvalid() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                getConnectionsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<Object> utilityBean = getConnectionsBL.clientValidation(null, requestParams);
        assertEquals(utilityBean.getUserId(), userId);
    }

    @Test
    void serverValidation_Success() throws Exception {
        AccountBean account = new AccountBean();
        account.setId(2);

        List<ControllerBean> services = new ArrayList<>();
        services.add(new ControllerBean());

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        when(controllerDao.getServicesList(2)).thenReturn(services);
        int accountId = getConnectionsBL.serverValidation(mockUtilityBean);
        assertEquals(accountId, 2);
    }

    @Test
    void serverValidation_No_Services() throws Exception {
        AccountBean account = new AccountBean();
        account.setId(2);

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        when(controllerDao.getServicesList(2)).thenReturn(new ArrayList<>());
        int accountId = getConnectionsBL.serverValidation(mockUtilityBean);
        assertEquals(accountId, 0);

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        when(controllerDao.getServicesList(2)).thenThrow(ControlCenterException.class);
        accountId = getConnectionsBL.serverValidation(mockUtilityBean);
        assertEquals(accountId, 0);
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                getConnectionsBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Failure() throws Exception {
        List<GetConnectionPojo> data = getConnectionsBL.process(0);
        assertEquals(data.size(), 0);
    }

    @Test
    void process_Success() throws Exception {
        when(connectionDetailsDao.getConnectionsByAccountId(2)).thenReturn(healConnectionDetailsBean);
        when(autoDiscoveryDao.getDiscoveredConnectionsList()).thenReturn(discoveredConnectionsBeanList);
        when(dateTimeUtil.getGMTToEpochTime("2021-06-15 23:28:01")).thenReturn(anyLong());
        List<GetConnectionPojo> data = getConnectionsBL.process(2);
        assertEquals(data.size(), 2);
    }
}
