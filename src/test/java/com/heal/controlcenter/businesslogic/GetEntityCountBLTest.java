package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.enums.SetupTypes;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.UserValidationUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;

import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({GetEntityCountBL.class})
class GetEntityCountBLTest {

    @Autowired
    @InjectMocks
    GetEntityCountBL getEntityCountBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    ActionScriptDao actionScriptDao;
    @Mock
    CategoryDao categoryDao;
    @Mock
    KPIDao kpiDao;
    @Mock
    MasterDataDao masterDataDao;
    @Mock
    UserValidationUtil userValidationUtil;

    String[] requestParams = new String[3];
    UtilityBean<InstancesBean> mockUtilityBean = null;
    InstancesBean instancesBean = new InstancesBean();
    List<CompInstClusterDetailsBean> details = new ArrayList<>();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";
        requestParams[2] = "SERVICE";

        instancesBean.setTypeName(SetupTypes.SERVICE.name());
        instancesBean.setUser("mockUserId");
        instancesBean.setAccountId(2);

        CompInstClusterDetailsBean compInstClusterDetailsBean = new CompInstClusterDetailsBean();
        compInstClusterDetailsBean.setComponentTypeName("Host");
        compInstClusterDetailsBean.setCompId(1);
        details.add(compInstClusterDetailsBean);

        mockUtilityBean = UtilityBean.<InstancesBean>builder()
                .userId("mockUserId")
                .authToken("mockAuthToken")
                .accountIdentifier("mockAccountIdentifier")
                .pojoObject(instancesBean)
                .build();
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getEntityCountBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getEntityCountBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                getEntityCountBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_AuthKeyInvalid() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                getEntityCountBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_InvalidType() {
        requestParams[2] = "";
        String expectedMessage = "ClientException : Invalid type name.";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getEntityCountBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Service_Type() throws DataProcessingException {
        mockUtilityBean.getPojoObject().setType(SetupTypes.SERVICE);
        UserAccessDetails details = new UserAccessDetails();
        details.setServiceIds(new ArrayList<>());
        when(userValidationUtil.getUserAccessDetails(any(), any())).thenReturn(details);
        Map<String, Object> data = getEntityCountBL.process(mockUtilityBean.getPojoObject());
        assertEquals(data.size(), 1);
        assertEquals(data.get(Constants.TOTAL), 0);
    }

    @Test
    void process_Service_Type_Failure() throws DataProcessingException {
        mockUtilityBean.getPojoObject().setType(SetupTypes.SERVICE);
        when(userValidationUtil.getUserAccessDetails(any(), any())).thenReturn(null);
        Map<String, Object> data = getEntityCountBL.process(mockUtilityBean.getPojoObject());
        assertEquals(data.size(), 1);
        assertEquals(data.get(Constants.TOTAL), 0);
    }

    @Test
    void process_Application_Type() throws DataProcessingException {
        mockUtilityBean.getPojoObject().setType(SetupTypes.APPLICATION);
        UserAccessDetails details = new UserAccessDetails();
        details.setApplicationIdentifiers(new ArrayList<>());
        when(userValidationUtil.getUserAccessDetails(any(), any())).thenReturn(details);
        Map<String, Object> data = getEntityCountBL.process(mockUtilityBean.getPojoObject());
        assertEquals(data.size(), 1);
        assertEquals(data.get(Constants.TOTAL), 0);
    }

    @Test
    void process_Application_Type_Failure() throws DataProcessingException {
        mockUtilityBean.getPojoObject().setType(SetupTypes.APPLICATION);
        when(userValidationUtil.getUserAccessDetails(any(), any())).thenReturn(null);
        Map<String, Object> data = getEntityCountBL.process(mockUtilityBean.getPojoObject());
        assertEquals(data.size(), 1);
        assertEquals(data.get(Constants.TOTAL), 0);
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<InstancesBean> utilityBean = getEntityCountBL.clientValidation(null, requestParams);
        assertEquals(utilityBean.getPojoObject().getTypeName(), "SERVICE");
    }

    @Test
    void serverValidation_Success() throws Exception {
        AccountBean account = new AccountBean();
        account.setId(1);

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        getEntityCountBL.serverValidation(mockUtilityBean);
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                getEntityCountBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Success_FORENSIC() throws Exception {
        instancesBean.setTypeName("FORENSIC");
        instancesBean.setType(SetupTypes.FORENSIC);

        when(actionScriptDao.getForensicCountForAccount(instancesBean.getAccountId())).thenReturn(5);
        Map<String, Object> data = getEntityCountBL.process(instancesBean);
        assertEquals(data.get(Constants.TOTAL), 5);
    }

    @Test
    void process_Success_CATEGORY() throws Exception {
        instancesBean.setTypeName("CATEGORY");
        instancesBean.setType(SetupTypes.CATEGORY);

        when(categoryDao.getCategoryCountForAccount(instancesBean.getAccountId())).thenReturn(3);
        Map<String, Object> data = getEntityCountBL.process(instancesBean);
        assertEquals(data.get(Constants.TOTAL), 3);
    }

    @Test
    void process_Success_HOST() throws Exception {
        instancesBean.setTypeName("HOST");
        instancesBean.setType(SetupTypes.HOST);

        when(masterDataDao.getCompInstanceDetails(instancesBean.getAccountId())).thenReturn(details);
        Map<String, Object> data = getEntityCountBL.process(instancesBean);
        assertEquals(data.get(Constants.TOTAL), 1L);
    }

    @Test
    void process_Failure_METRICES() throws Exception {
        String expectedMessage = "DataProcessingException : Error occurred while getting KPI count.";

        instancesBean.setTypeName("METRICES");
        instancesBean.setType(SetupTypes.METRICES);
        ControlCenterException controlCenterException = new ControlCenterException("Error occurred while getting KPI count.");
        when(kpiDao.getKpiCountForAccount(instancesBean.getAccountId())).thenThrow(controlCenterException);
        DataProcessingException requestException = assertThrows(DataProcessingException.class, () ->
                getEntityCountBL.process(instancesBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Success_METRICES() throws Exception {
        instancesBean.setTypeName("METRICES");
        instancesBean.setType(SetupTypes.METRICES);
        when(kpiDao.getKpiCountForAccount(instancesBean.getAccountId())).thenReturn(10);
        Map<String, Object> data = getEntityCountBL.process(instancesBean);
        assertEquals(data.size(), 1);
        assertEquals(data.get(Constants.TOTAL), 10L);
    }
}
