package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.ServiceConfigurationDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AccountServiceKey;
import com.heal.controlcenter.pojo.ServiceSuppPersistenceConfigPojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.UserValidationUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

class GetServiceSuppPersistenceBLTest {
    @InjectMocks
    private GetServiceSuppPersistenceBL getServiceSuppPersistenceBL;

    @Mock
    private AccountsDao accountsDao;

    @Mock
    private ControllerDao controllerDao;

    @Mock
    private UserValidationUtil userValidationUtil;

    @Mock
    private CommonUtils commonUtils;

    @Mock
    private ServiceConfigurationDao serviceConfigurationDao;

    @Mock
    private DateTimeUtil dateTimeUtil;

    UtilityBean<String> mockUtilityBean;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void clientValidation_NullAuthKey_ThrowsClientException() {
        String[] params = {"", "accountIdentifier", "serviceIdentifier"};
        assertThrows(ClientException.class, () ->
                getServiceSuppPersistenceBL.clientValidation(null, params));
    }

    @Test
    void clientValidation_EmptyAccountIdentifier_ThrowsClientException() {
        String[] params = {"authKey", "", "serviceIdentifier"};
        assertThrows(ClientException.class, () ->
                getServiceSuppPersistenceBL.clientValidation(null, params));
    }

    @Test
    void clientValidation_EmptyServiceIdentifier_ThrowsClientException() {
        String[] params = {"authKey", "accountIdentifier", ""};
        assertThrows(ClientException.class, () ->
                getServiceSuppPersistenceBL.clientValidation(null, params));
    }

    @Test
    void clientValidation_UserIdExtractionFailure_ThrowsClientException() throws Exception {
        String[] params = {"authKey", "accountIdentifier", "serviceIdentifier"};
        when(commonUtils.getUserId("authKey")).thenThrow(new RuntimeException("Error extracting userId"));

        assertThrows(ClientException.class, () ->
                getServiceSuppPersistenceBL.clientValidation(null, params));
        verify(commonUtils, times(1)).getUserId("authKey");
    }

    @Test
    void clientValidation_ValidInput_ReturnsUtilityBean() throws Exception {
        String[] params = {"authKey", "accountIdentifier", "serviceIdentifier"};
        when(commonUtils.getUserId("authKey")).thenReturn("userId");

        UtilityBean result = getServiceSuppPersistenceBL.clientValidation(null, params);

        assertNotNull(result);
        assertEquals("userId", result.getUserId());
        verify(commonUtils, times(1)).getUserId("authKey");
    }

    @Test
    void serverValidation_AccountNotFound_ThrowsServerException() {
        mockUtilityBean = UtilityBean.<String>builder()
                .userId("mockUserId")
                .authToken("mockAuthToken")
                .accountIdentifier("mockAccountIdentifier")
                .requestParams(new HashMap<>() {{
                    put(Constants.ACCOUNT_IDENTIFIER, "invalidAccountIdentifier");
                    put(Constants.SERVICE_IDENTIFIER, "mockServiceIdentifier");
                    put(Constants.AUTH_KEY, "mockAuthToken");
                }})
                .build();
        when(accountsDao.getAccountDetailsForIdentifier("invalidAccountIdentifier")).thenReturn(null);

        assertThrows(ServerException.class, () ->
                getServiceSuppPersistenceBL.serverValidation(mockUtilityBean));
        verify(accountsDao, times(1)).getAccountDetailsForIdentifier("invalidAccountIdentifier");
    }

    @Test
    void serverValidation_ServiceNotFound_ThrowsServerException() {
        mockUtilityBean = UtilityBean.<String>builder()
                .userId("mockUserId")
                .authToken("authKey")
                .accountIdentifier("accountIdentifier")
                .requestParams(new HashMap<>() {{
                    put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
                    put(Constants.SERVICE_IDENTIFIER, "invalidServiceIdentifier");
                    put(Constants.AUTH_KEY, "authKey");
                }})
                .build();
        AccountBean account = new AccountBean();
        account.setId(1);
        when(accountsDao.getAccountDetailsForIdentifier("accountIdentifier")).thenReturn(account);
        when(controllerDao.getServiceByIdentifier("invalidServiceIdentifier", 1)).thenReturn(null);

        assertThrows(ServerException.class, () ->
                getServiceSuppPersistenceBL.serverValidation(mockUtilityBean));
        verify(accountsDao, times(1)).getAccountDetailsForIdentifier("accountIdentifier");
        verify(controllerDao, times(1)).getServiceByIdentifier("invalidServiceIdentifier", 1);
    }

    @Test
    void serverValidation_UserAccessDetailsNotFound_ThrowsServerException() {
        mockUtilityBean = UtilityBean.<String>builder()
                .userId(null)
                .authToken("authKey")
                .accountIdentifier("accountIdentifier")
                .requestParams(new HashMap<>() {{
                    put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
                    put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");
                    put(Constants.AUTH_KEY, "authKey");
                }})
                .build();
        AccountBean account = new AccountBean();
        account.setId(1);
        account.setIdentifier("accountIdentifier");
        ControllerBean service = new ControllerBean();
        service.setId(2);

        when(accountsDao.getAccountDetailsForIdentifier("accountIdentifier")).thenReturn(account);
        when(controllerDao.getServiceByIdentifier("serviceIdentifier", 1)).thenReturn(service);
        when(userValidationUtil.getUserAccessDetails(null, "accountIdentifier")).thenReturn(null);

        assertThrows(ServerException.class, () ->
                getServiceSuppPersistenceBL.serverValidation(mockUtilityBean));
        verify(accountsDao, times(1)).getAccountDetailsForIdentifier("accountIdentifier");
        verify(controllerDao, times(1)).getServiceByIdentifier("serviceIdentifier", 1);
        verify(userValidationUtil, times(1)).getUserAccessDetails(null, "accountIdentifier");
    }

    @Test
    void serverValidation_ValidInput_ReturnsAccountServiceKey() throws ServerException {
        mockUtilityBean = UtilityBean.<String>builder()
                .userId(null)
                .authToken("authKey")
                .accountIdentifier("accountIdentifier")
                .requestParams(new HashMap<>() {{
                    put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
                    put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");
                    put(Constants.AUTH_KEY, "authKey");
                }})
                .build();
        AccountBean account = new AccountBean();
        account.setId(1);
        account.setIdentifier("accountIdentifier");
        ControllerBean service = new ControllerBean();
        service.setId(2);
        UserAccessDetails userAccessDetails = new UserAccessDetails();

        when(accountsDao.getAccountDetailsForIdentifier("accountIdentifier")).thenReturn(account);
        when(controllerDao.getServiceByIdentifier("serviceIdentifier", 1)).thenReturn(service);
        when(userValidationUtil.getUserAccessDetails(null, "accountIdentifier")).thenReturn(userAccessDetails);

        AccountServiceKey result = getServiceSuppPersistenceBL.serverValidation(mockUtilityBean);

        assertNotNull(result);
        assertEquals(account, result.getAccount());
        assertEquals("authKey", result.getAuthorizationKey());
        assertEquals(2, result.getServiceId());
        verify(accountsDao, times(1)).getAccountDetailsForIdentifier("accountIdentifier");
        verify(controllerDao, times(1)).getServiceByIdentifier("serviceIdentifier", 1);
        verify(userValidationUtil, times(1)).getUserAccessDetails(null, "accountIdentifier");
    }

    @Test
    void process_ValidInput_ReturnsServiceConfigDetails() throws Exception {
        int accountId = 1;
        int serviceId = 2;
        String userId = "userId";
        String authKey = "authKey";
        AccountBean accountBean = new AccountBean();
        accountBean.setId(1);
        AccountServiceKey accountServiceKey = new AccountServiceKey(accountBean, authKey, serviceId);

        List<ServiceSuppPersistenceConfigurationBean> serviceBeanList = new ArrayList<>();
        serviceBeanList.add(ServiceSuppPersistenceConfigurationBean.builder()
                .id(1)
                .startCollectionInterval(1)
                .lowPersistence(10)
                .lowSuppression(20)
                .mediumPersistence(30)
                .mediumSuppression(40)
                .highPersistence(50)
                .highSuppression(60)
                .highEnable(true)
                .mediumEnable(true)
                .lowEnable(true)
                .build());

        when(commonUtils.getUserId(authKey)).thenReturn(userId);
        when(serviceConfigurationDao.getServiceConfiguration(anyInt(), anyInt())).thenReturn(null).thenReturn(serviceBeanList);
        when(serviceConfigurationDao.addServiceConfiguration(anyList())).thenReturn(new int[]{1, 2});

        Map<String, ServiceSuppPersistenceConfigPojo> result = getServiceSuppPersistenceBL.process(accountServiceKey);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(Constants.OPERATOR_LESS_THAN));
        verify(serviceConfigurationDao, times(2)).getServiceConfiguration(accountId, serviceId);
        verify(serviceConfigurationDao, times(1)).addServiceConfiguration(anyList());
        verify(commonUtils, times(1)).getUserId(authKey);
    }

}
