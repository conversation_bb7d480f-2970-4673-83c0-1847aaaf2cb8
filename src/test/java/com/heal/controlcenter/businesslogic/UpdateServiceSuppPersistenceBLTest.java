package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.AnomalyConfiguration;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.ServiceConfiguration;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.ServiceConfigurationDao;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AccountServiceKey;
import com.heal.controlcenter.pojo.ServiceSuppPersistenceConfigPojo;
import com.heal.controlcenter.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class UpdateServiceSuppPersistenceBLTest {

    @InjectMocks
    private UpdateServiceSuppPersistenceBL updateServiceSuppPersistenceBL;

    @Mock
    private CommonUtils commonUtils;

    @Mock
    private AccountsDao accountsDao;

    @Mock
    private ControllerDao controllerDao;

    @Mock
    private UserValidationUtil userValidationUtil;

    @Mock
    private ServiceConfigurationDao serviceConfigurationDao;

    @Mock
    private ServiceRepo serviceRepo;

    @Mock
    private DateTimeUtil dateTimeUtil;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void clientValidation_NullAuthKey_ThrowsClientException() {
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        String[] params = {null, "accountIdentifier", "serviceIdentifier"};

        ClientException exception = assertThrows(ClientException.class, () ->
                updateServiceSuppPersistenceBL.clientValidation(serviceConfigDetails, params));
        assertEquals("ClientException : " + UIMessages.AUTH_KEY_EXCEPTION_MESSAGE, exception.getMessage());
    }

    @Test
    void clientValidation_EmptyAccountIdentifier_ThrowsClientException() {
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        String[] params = {"authKey", "", "serviceIdentifier"};

        ClientException exception = assertThrows(ClientException.class, () ->
                updateServiceSuppPersistenceBL.clientValidation(serviceConfigDetails, params));
        assertEquals("ClientException : " + UIMessages.ACCOUNT_IDENTIFIER_EXCEPTION_MESSAGE, exception.getMessage());
    }

    @Test
    void clientValidation_EmptyServiceIdentifier_ThrowsClientException() {
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        String[] params = {"authKey", "accountIdentifier", ""};

        ClientException exception = assertThrows(ClientException.class, () ->
                updateServiceSuppPersistenceBL.clientValidation(serviceConfigDetails, params));
        assertEquals("ClientException : " + UIMessages.SERVICE_IDENTIFIER_EXCEPTION_MESSAGE, exception.getMessage());
    }

    @Test
    void clientValidation_UserIdExtractionFailure_ThrowsClientException() throws Exception {
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        String[] params = {"authKey", "accountIdentifier", "serviceIdentifier"};
        when(commonUtils.getUserId("authKey")).thenThrow(new RuntimeException("Error extracting userId"));

        ClientException exception = assertThrows(ClientException.class, () ->
                updateServiceSuppPersistenceBL.clientValidation(serviceConfigDetails, params));
        assertEquals("ClientException : " + UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE, exception.getMessage());
        verify(commonUtils, times(1)).getUserId("authKey");
    }

    @Test
    void clientValidation_EmptyServiceConfigDetails_ThrowsClientException() throws Exception {
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        String[] params = {"authKey", "accountIdentifier", "serviceIdentifier"};
        when(commonUtils.getUserId("authKey")).thenReturn("userId");

        ClientException exception = assertThrows(ClientException.class, () ->
                updateServiceSuppPersistenceBL.clientValidation(serviceConfigDetails, params));
        assertEquals("ClientException : Request object should contain service configuration details.", exception.getMessage());
    }

    @Test
    void clientValidation_ValidInput_ReturnsUtilityBean() throws Exception {
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        serviceConfigDetails.put("key", new ServiceSuppPersistenceConfigPojo());
        String[] params = {"authKey", "accountIdentifier", "serviceIdentifier"};
        when(commonUtils.getUserId("authKey")).thenReturn("userId");

        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> result =
                updateServiceSuppPersistenceBL.clientValidation(serviceConfigDetails, params);

        assertNotNull(result);
        assertEquals("userId", result.getUserId());
        assertEquals(serviceConfigDetails, result.getPojoObject());
        verify(commonUtils, times(1)).getUserId("authKey");
    }

    @Test
    void serverValidation_AccountNotFound_ThrowsServerException() {
        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, "invalidAccountIdentifier");
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");

        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> utilityBean =
                UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
                        .requestParams(requestParamsMap)
                        .userId(null)
                        .build();

        when(accountsDao.getAccountDetailsForIdentifier("invalidAccountIdentifier")).thenReturn(null);

        ServerException exception = assertThrows(ServerException.class, () ->
                updateServiceSuppPersistenceBL.serverValidation(utilityBean));

        assertEquals("ServerException : " + String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, "invalidAccountIdentifier"), exception.getMessage());
        verify(accountsDao, times(1)).getAccountDetailsForIdentifier("invalidAccountIdentifier");
    }

    @Test
    void serverValidation_ServiceNotFound_ThrowsServerException() {
        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, "invalidServiceIdentifier");

        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> utilityBean =
                UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
                        .requestParams(requestParamsMap)
                        .userId(null)
                        .build();
        AccountBean account = new AccountBean();
        account.setId(1);
        account.setIdentifier("accountIdentifier");
        when(accountsDao.getAccountDetailsForIdentifier("accountIdentifier")).thenReturn(account);
        when(controllerDao.getServiceByIdentifier("invalidServiceIdentifier", 1)).thenReturn(null);

        ServerException exception = assertThrows(ServerException.class, () ->
                updateServiceSuppPersistenceBL.serverValidation(utilityBean));
        assertEquals("ServerException : " + String.format(UIMessages.SERVICE_IDENTIFIER_UNAVAILABLE, "invalidServiceIdentifier"), exception.getMessage());
        verify(accountsDao, times(1)).getAccountDetailsForIdentifier("accountIdentifier");
        verify(controllerDao, times(1)).getServiceByIdentifier("invalidServiceIdentifier", 1);
    }

    @Test
    void serverValidation_ServiceConfigNotFound_ThrowsServerException() {
        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");

        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> utilityBean =
                UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
                        .requestParams(requestParamsMap)
                        .userId("userId")
                        .build();
        AccountBean account = new AccountBean();
        account.setId(1);
        account.setIdentifier("accountIdentifier");
        ControllerBean service = new ControllerBean();
        service.setId(2);
        when(accountsDao.getAccountDetailsForIdentifier("accountIdentifier")).thenReturn(account);
        when(controllerDao.getServiceByIdentifier("serviceIdentifier", 1)).thenReturn(service);
        when(serviceConfigurationDao.getServiceConfiguration(1, 2)).thenReturn(null);
        when(userValidationUtil.getUserAccessDetails("userId", "accountIdentifier")).thenReturn(new UserAccessDetails());

        ServerException exception = assertThrows(ServerException.class, () ->
                updateServiceSuppPersistenceBL.serverValidation(utilityBean));
        assertEquals("ServerException : Service configuration is unavailable for the provided service id", exception.getMessage());
        verify(serviceConfigurationDao, times(1)).getServiceConfiguration(1, 2);
    }

    @Test
    void serverValidation_UserAccessDetailsNull_ThrowsServerException() {
        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");

        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> utilityBean =
                UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
                        .requestParams(requestParamsMap)
                        .userId("userId")
                        .build();
        AccountBean account = new AccountBean();
        account.setId(1);
        account.setIdentifier("accountIdentifier");
        ControllerBean service = new ControllerBean();
        service.setId(2);

        when(accountsDao.getAccountDetailsForIdentifier("accountIdentifier")).thenReturn(account);
        when(controllerDao.getServiceByIdentifier("serviceIdentifier", 1)).thenReturn(service);
        when(userValidationUtil.getUserAccessDetails("userId", "accountIdentifier")).thenReturn(null);

        ServerException exception = assertThrows(ServerException.class, () ->
                updateServiceSuppPersistenceBL.serverValidation(utilityBean));
        assertEquals("ServerException : User access details unavailable", exception.getMessage());
        verify(userValidationUtil, times(1)).getUserAccessDetails("userId", "accountIdentifier");
    }

    @Test
    void serverValidation_ValidInput_ReturnsAccountServiceKey() throws ServerException, ControlCenterException {
        AccountBean account = new AccountBean();
        account.setId(1);
        account.setIdentifier("accountIdentifier");
        ControllerBean service = new ControllerBean();
        service.setId(2);
        List<ServiceSuppPersistenceConfigurationBean> serviceConfigList = new ArrayList<>();
        ServiceSuppPersistenceConfigurationBean newBean = new ServiceSuppPersistenceConfigurationBean();
        newBean.setId(2);
        serviceConfigList.add(newBean);
        when(accountsDao.getAccountDetailsForIdentifier("accountIdentifier")).thenReturn(account);
        when(controllerDao.getServiceByIdentifier("serviceIdentifier", 1)).thenReturn(service);
        when(serviceConfigurationDao.getServiceConfiguration(1, 2)).thenReturn(serviceConfigList);
        when(userValidationUtil.getUserAccessDetails("userId", "accountIdentifier")).thenReturn(new UserAccessDetails());

        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();
        ServiceSuppPersistenceConfigPojo configPojo = new ServiceSuppPersistenceConfigPojo();
        configPojo.setServiceConfigId(2);
        serviceConfigDetails.put("key", configPojo);
        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> inputBean =
                UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
                        .userId("userId")
                        .pojoObject(serviceConfigDetails)
                        .requestParams(new HashMap<>() {{
                            put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
                            put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");
                            put(Constants.AUTH_KEY, "authKey");
                        }})
                        .build();

        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> validatedBean =
                updateServiceSuppPersistenceBL.serverValidation(inputBean);

        AccountServiceKey result = new AccountServiceKey(
                account,
                validatedBean.getRequestParams().get(Constants.AUTH_KEY),
                Integer.parseInt(validatedBean.getRequestParams().get(Constants.SERVICE_ID))
        );

        assertNotNull(result);
        assertEquals(account, result.getAccount());
        assertEquals("authKey", result.getAuthorizationKey());
        assertEquals(2, result.getServiceId());
        verify(accountsDao, times(1)).getAccountDetailsForIdentifier("accountIdentifier");
        verify(controllerDao, times(1)).getServiceByIdentifier("serviceIdentifier", 1);
        verify(serviceConfigurationDao, times(1)).getServiceConfiguration(1, 2);
    }

    @Test
    void process_ValidInput_Success() throws Exception {
        AccountBean accountBean = new AccountBean();
        accountBean.setIdentifier("accountIdentifier");
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetailsMap = new HashMap<>();
        HashMap<String, String> requestParams = new HashMap<>();
        requestParams.put(Constants.SERVICE_ID, "2");
        requestParams.put(Constants.ACCOUNT_ID, "1");
        requestParams.put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
        requestParams.put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");
        requestParams.put(Constants.AUTH_KEY, "authKey");

        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> inputBean =
                UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
                        .userId("userId")
                        .pojoObject(serviceConfigDetailsMap)
                        .requestParams(new HashMap<>() {{
                            put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
                            put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");
                            put(Constants.AUTH_KEY, "authKey");
                        }})
                        .build();

        ServiceSuppPersistenceConfigPojo configPojo = new ServiceSuppPersistenceConfigPojo();
        configPojo.setServiceConfigId(1);
        configPojo.setLowPersistence(10);
        configPojo.setMediumPersistence(20);
        configPojo.setHighPersistence(30);
        configPojo.setLowSuppression(40);
        configPojo.setMediumSuppression(50);
        configPojo.setHighSuppression(60);
        configPojo.setLowEnable(true);
        configPojo.setMediumEnable(true);
        configPojo.setHighEnable(true);
        serviceConfigDetailsMap.put(Constants.OPERATOR_LESS_THAN, configPojo);
        serviceConfigDetailsMap.put(Constants.OPERATOR_GREATER_THAN, configPojo);
        BasicEntity serviceEntity = new BasicEntity();
        serviceEntity.setId(2);
        serviceEntity.setIdentifier("serviceIdentifier");

        Service service = new Service();
        service.setId(2);
        ServiceConfiguration serviceConfiguration = new ServiceConfiguration();
        AnomalyConfiguration anomalyConfiguration = new AnomalyConfiguration();
        anomalyConfiguration.setEndCollectionInterval(58);
        serviceConfiguration.setAnomalyConfiguration(anomalyConfiguration);

        ServiceConfiguration serviceConfiguration2 = new ServiceConfiguration();
        AnomalyConfiguration anomalyConfiguration2 = new AnomalyConfiguration();
        anomalyConfiguration2.setEndCollectionInterval(80);
        serviceConfiguration2.setAnomalyConfiguration(anomalyConfiguration2);

        service.setServiceConfigurations(Arrays.asList(serviceConfiguration, serviceConfiguration2));


        when(commonUtils.getUserId("authKey")).thenReturn("userId");
        when(dateTimeUtil.getTimeInGMT(anyLong())).thenReturn(String.valueOf(System.currentTimeMillis()));
        when(serviceRepo.getAllServicesDetails(anyString())).thenReturn(Collections.singletonList(serviceEntity));
        when(serviceRepo.getServiceConfigurationByIdentifier(anyString(), anyString())).thenReturn(service);
        when(serviceRepo.getAllServicesDetails(anyString())).thenReturn(Arrays.asList(serviceEntity));

        assertDoesNotThrow(() -> updateServiceSuppPersistenceBL.process(inputBean));
        verify(serviceConfigurationDao, times(1)).updateServiceSuppPersistenceConfig(anyList());
        verify(serviceRepo, times(1)).updateServiceConfigurationByServiceIdentifier(anyString(), anyString(), any());
    }

}
