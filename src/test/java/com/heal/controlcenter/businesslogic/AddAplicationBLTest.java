package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.pojo.Application;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;


import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class AddAplicationBLTest {

    @Autowired
    @InjectMocks
    AddApplicationsBL applications;

    Application application = new Application();

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Disabled
    @Test
    void addClientValidations_InvalidRequest()  {
        String expectedMessage = "ClientException : Request object is null or empty.";
        ClientException requestException = assertThrows(ClientException.class, () ->
                applications.clientValidation(null,null));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void addClientValidations_NullAuthToken() {
        String expectedMessage = "ClientException : Invalid authorization token";
        application.setName("test_app");
        application.setIdentifier("");
        String[] params = new String[2];
        params[0] = "";
        params[1] = "d681ef13-d690-4917-jkhg-6c79b-1";
        ClientException requestException = assertThrows(ClientException.class, () ->
                applications.clientValidation(application, params));
        System.out.println(requestException.getMessage());
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Disabled
    @Test
    void addClientValidations_InvalidAuthToken() {
        String expectedMessage = "ClientException : Unable to fetch userId from authorization token";
        application.setName("test_app");
        application.setIdentifier("");
        String[] params = new String[2];
        params[0] = "nfrejknfjerngkj";
        params[1] = "d681ef13-d690-4917-jkhg-6c79b-1";
        ClientException requestException = assertThrows(ClientException.class, () ->
                applications.clientValidation(application, params));
        System.out.println(requestException.getMessage());
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }




}
