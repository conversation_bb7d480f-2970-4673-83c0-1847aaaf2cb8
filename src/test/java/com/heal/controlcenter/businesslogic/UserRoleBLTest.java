package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.util.CommonUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;


import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
@PrepareForTest({UserRoleBL.class})
public class UserRoleBLTest {

    @Mock
    CommonUtils commonUtils;
    @Autowired
    @InjectMocks
    UserRoleBL userRoleBL;
    @Mock
    UserDao rolesAndProfilesDao;


    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Disabled
    @Test
    void clientValidation_InvalidToken() throws ControlCenterException {
        String expectedMessage = "ClientException : Invalid Authorization token";
        when(commonUtils.getUserId("tyr8-uijjf8sooijfjfkjkjskjjkje")).thenReturn(any());
        ClientException exception = assertThrows(ClientException.class, () ->
                userRoleBL.clientValidation(null, "tyr8-uijjf8sooijfjfkjkjskjjkje" ));
        assertEquals(expectedMessage, exception.getMessage());
    }

    @Test
    void clientValidation() throws ControlCenterException, ClientException {
        String userId = "7640123a-fbde-4fe5-9812-581cd1e3a9c1";
        when(commonUtils.getUserId("tyr8-uijjf8sooijfjfkjkjskjjkje")).thenReturn(userId);
        UtilityBean<String> auth = userRoleBL.clientValidation(null, "tyr8-uijjf8sooijfjfkjkjskjjkje" );
        assertEquals(auth.getPojoObject(), userId);
    }


    @Disabled
    @Test
    void clientValidation_WhenUserIdFailed() throws ControlCenterException {
        String message = "Error while fetching userId from the Authorization token";
        given(commonUtils.getUserId(anyString())).willAnswer(exc -> {
            throw new ControlCenterException(message);
        });
        Throwable processing = assertThrows(ClientException.class, () -> userRoleBL.clientValidation(null,"tyr8-uijjf8sooijfjfkjkjskjjkje"));
        assertTrue(processing.getMessage().contains(message));
    }

    @Test
    void process_when_user_profile_invalid() {
        Throwable processing = assertThrows(DataProcessingException.class, () -> userRoleBL.process("bean"));
        assertTrue(processing.getMessage().contains("DataProcessingException : User roles information unavailable"));
    }
}
