package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMSDetailsPojo;
import com.heal.controlcenter.util.CommonUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@PrepareForTest({GetSMSConfigurationsBL.class})
class GetSMSConfigurationsBLTest {

    @InjectMocks
    GetSMSConfigurationsBL getSMSConfigurationsBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    NotificationsDao notificationsDao;
    @Mock
    MasterDataDao masterDataDao;

    String[] requestParams = new String[2];
    UtilityBean<Object> mockUtilityBean = null;
    SMSDetailsBean smsDetailsBean = new SMSDetailsBean();
    List<SMSParameterBean> smsParametersList = new ArrayList<>();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        mockUtilityBean = UtilityBean.builder()
                .userId("mockUserId")
                .authToken("mockAuthToken")
                .accountIdentifier("mockAccountIdentifier")
                .build();

        smsDetailsBean.setAddress("www.healsoftware.ai");
        smsDetailsBean.setPort(8996);
        smsDetailsBean.setCountryCode("91");
        smsDetailsBean.setProtocolId(161);
        smsDetailsBean.setHttpMethod("GET");
        smsDetailsBean.setHttpRelativeUrl("/home/<USER>");
        smsDetailsBean.setIsMultiRequest(0);

        SMSParameterBean smsParameterBean = new SMSParameterBean();
        smsParameterBean.setParameterName("Phone number");
        smsParameterBean.setParameterTypeId(165);
        smsParameterBean.setParameterValue("{MobileNumber}");
        smsParameterBean.setIsPlaceholder(1);
        smsParametersList.add(smsParameterBean);
    }

    @AfterEach
    void tearDown() {
        requestParams = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getSMSConfigurationsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getSMSConfigurationsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                getSMSConfigurationsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_AuthKeyInvalid() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(any());
        ClientException requestException = assertThrows(ClientException.class, () ->
                getSMSConfigurationsBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<Object> utilityBean = getSMSConfigurationsBL.clientValidation(null, requestParams);
        assertEquals(utilityBean.getUserId(), userId);
    }

    @Test
    void serverValidation_Success() throws Exception {
        AccountBean account = new AccountBean();
        account.setId(1);

        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        Integer accountId = getSMSConfigurationsBL.serverValidation(mockUtilityBean);
        assertEquals(accountId, account.getId());
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                getSMSConfigurationsBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Success() throws Exception {
        ViewTypesBean queryParameter = new ViewTypesBean();
        queryParameter.setSubTypeName("QueryParameter");
        queryParameter.setTypeId(54);
        queryParameter.setSubTypeId(165);
        queryParameter.setTypeName("SMSParameterTypes");

        when(notificationsDao.getSMSDetails(2)).thenReturn(smsDetailsBean);
        when(notificationsDao.getSMSParameters(anyInt())).thenReturn(smsParametersList);
        when(masterDataDao.getMstSubTypeBySubTypeId(anyInt())).thenReturn(queryParameter);
        SMSDetailsPojo data = getSMSConfigurationsBL.process(2);
        assertEquals(data.getAddress(), "www.healsoftware.ai");
    }

    @Test
    void process_Success_NoConfigurations() throws Exception {
        when(notificationsDao.getSMSDetails(2)).thenReturn(smsDetailsBean);
        SMSDetailsPojo data = getSMSConfigurationsBL.process(2);
        assertNull(data);
    }
}
