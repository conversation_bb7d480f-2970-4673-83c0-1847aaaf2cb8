package com.heal.controlcenter.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.controlcenter.beans.TagBean;
import com.heal.controlcenter.beans.ThresholdSeverityBean;
import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.businesslogic.PostAccountService;
import com.heal.controlcenter.pojo.AccountRequest;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class AccountControllerIT {

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @Mock
    private GetAccountsBL getAccountsBL;

    @Mock
    private PostAccountService postAccountService;

    @Mock
    private JsonFileParser jsonFileParser;

    @InjectMocks
    private AccountController accountController;

    private static final String MOCK_AUTH_TOKEN = "Bearer mock-jwt-token";
    private static final String MOCK_USER_ID = "test-user-123";

    @BeforeEach
    void setup() throws Exception {
        mockMvc = MockMvcBuilders.standaloneSetup(accountController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void getAllAccounts_WhenSuccess() throws Exception {
        // Arrange
        HttpHeaders mockHeaders = new HttpHeaders();
        mockHeaders.set("authorization", "mockAuthorization");

        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(mockHeaders);
        when(getAccountsBL.clientValidation(null, MOCK_AUTH_TOKEN)).thenReturn(null);
        when(getAccountsBL.process(any())).thenReturn(createMockAccounts());

        // Act & Assert
        mockMvc.perform(get("/accounts")
                .header("Authorization", MOCK_AUTH_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("Accounts fetched successfully."))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.responseStatus").value("OK"));
    }

    @Test
    void getAllAccounts_WhenUnauthorized() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/accounts"))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    @Test
    void createAccount_WhenSuccess() throws Exception {
        // Arrange
        AccountRequest request = createValidAccountRequest();
        com.heal.controlcenter.beans.PostAccountBean mockResponse = createMockPostAccountBean();

        when(postAccountService.createAccount(any())).thenReturn(1);
        when(postAccountService.getAccountById(1)).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(post("/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.identifier").value("test-account-001"))
                .andExpect(jsonPath("$.accountName").value("Test Account"))
                .andExpect(jsonPath("$.status").value(1));
    }

    private List<com.heal.controlcenter.pojo.Account> createMockAccounts() {
        List<com.heal.controlcenter.pojo.Account> accounts = new ArrayList<>();

        accounts.add(com.heal.controlcenter.pojo.Account.builder()
                .accountId(1)
                .accountName("Test Account 1")
                .identifier("test-account-001")
                .status(1)
                .publicKey("mock-public-key-1")
                .privateKey("mock-private-key-1")
                .build());

        accounts.add(com.heal.controlcenter.pojo.Account.builder()
                .accountId(2)
                .accountName("Test Account 2")
                .identifier("test-account-002")
                .status(1)
                .publicKey("mock-public-key-2")
                .privateKey("mock-private-key-2")
                .build());

        return accounts;
    }

    private AccountRequest createValidAccountRequest() {
        AccountRequest request = new AccountRequest();
        request.setIdentifier("test-account-001");
        request.setAccountName("Test Account");
        request.setClosingWindow("24h");
        request.setMaxDataBreaks("5");
        request.setStatus(1);

        // Add threshold severity
        ThresholdSeverityBean threshold = new ThresholdSeverityBean();
        threshold.setLow(true);
        threshold.setWarning(true);
        threshold.setCritical(false);
        request.setThresholdSeverityBean(threshold);

        // Add tags
        List<TagBean> tags = new ArrayList<>();
        TagBean tag = new TagBean();
        tag.setIdentifier("env-test");
        tag.setName("Environment");
        tags.add(tag);
        request.setTag(tags);

        return request;
    }

    private com.heal.controlcenter.beans.PostAccountBean createMockPostAccountBean() {
        com.heal.controlcenter.beans.PostAccountBean bean = new com.heal.controlcenter.beans.PostAccountBean();
        bean.setId(1);
        bean.setIdentifier("test-account-001");
        bean.setAccountName("Test Account");
        bean.setClosingWindow("24h");
        bean.setMaxDataBreaks("5");
        bean.setStatus(1);

        // Add threshold severity
        ThresholdSeverityBean threshold = new ThresholdSeverityBean();
        threshold.setId(1);
        threshold.setLow(true);
        threshold.setWarning(true);
        threshold.setCritical(false);
        bean.setThresholdSeverityBean(threshold);

        // Add tags
        List<TagBean> tags = new ArrayList<>();
        TagBean tag = new TagBean();
        tag.setId(1);
        tag.setIdentifier("env-test");
        tag.setName("Environment");
        tags.add(tag);
        bean.setTagBean(tags);

        return bean;
    }
}
