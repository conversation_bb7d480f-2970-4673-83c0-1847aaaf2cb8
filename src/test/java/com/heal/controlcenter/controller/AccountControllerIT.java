package com.heal.controlcenter.controller;

import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
@Import(AccountControllerIT.TestConfig.class)
class AccountControllerIT {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private GetAccountsBL getAccountsBL;

    @Autowired
    private JsonFileParser headersParser;

    private List<Account> mockAccountList;

    @TestConfiguration
    static class TestConfig {
        @Bean
        @Primary
        public GetAccountsBL getAccountsBL() {
            return Mockito.mock(GetAccountsBL.class);
        }

        @Bean
        @Primary
        public JsonFileParser jsonFileParser() {
            return Mockito.mock(JsonFileParser.class);
        }
    }

//    @BeforeEach
//    void setup() {
//        mockAccountList = new ArrayList<>();
//    }

    @Test
    void getAllAccounts_WhenSuccess_() throws Exception {
        // Arrange
        HttpHeaders mockHeaders = new HttpHeaders();
        mockHeaders.set("authorization", "mockAuthorization");

        when(headersParser.loadHeaderConfiguration()).thenReturn(mockHeaders);
        when(getAccountsBL.clientValidation(null, "mockAuthorization")).thenReturn(null);
        when(getAccountsBL.process(any())).thenReturn(List.of());

        // Act & Assert
        mockMvc.perform(get("/accounts").header("Authorization", "mockAuthorization"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.responseStatus").value("OK"));
    }

//    @Test
//    void getAllAccounts_WhenSuccess() throws Exception {
//        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
//            set("authorization", "mockAuthorization");
//        }});
//        when(getAccountsBL.clientValidation(any(), anyString())).thenReturn(null);
//        when(getAccountsBL.process(any())).thenReturn(mockAccountList);
//
//        mockMvc.perform(get("/accounts")
//                        .header("Authorization", "mockAuthorization"))
//                .andExpect(status().isOk())
//                .andExpect(header().string("authorization", "mockAuthorization"))
//                .andExpect(jsonPath("$.responseStatus").value("OK"))
//                .andExpect(jsonPath("$.message").value("Accounts fetched successfully."))
//                .andExpect(jsonPath("$.data").isArray())
//                .andExpect(jsonPath("$.data").isEmpty());
//    }
//
//    @Test
//    void getAllAccounts_WhenControlCenterException() throws Exception {
//        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
//            set("authorization", "mockAuthorization");
//        }});
//        when(getAccountsBL.clientValidation(any(), anyString())).thenThrow(RuntimeException.class);
//
//        mockMvc.perform(get("/accounts")
//                        .header("Authorization", "mockAuthorization"))
//                .andExpect(status().isBadRequest())
//                .andExpect(header().string("authorization", "mockAuthorization"))
//                .andExpect(jsonPath("$.responseStatus").value("BAD_REQUEST"))
//                .andExpect(jsonPath("$.data").exists());
//    }
}
