package com.heal.controlcenter.controller;

import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.businesslogic.PostAccountService;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class AccountControllerIT {

    private MockMvc mockMvc;

    @Mock
    private GetAccountsBL getAccountsBL;

    @Mock
    private PostAccountService postAccountService;

    @Mock
    private JsonFileParser headersParser;

    @InjectMocks
    private AccountController accountController;

    @BeforeEach
    void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(accountController).build();
    }

    @Test
    void getAllAccounts_WhenSuccess() throws Exception {
        // Arrange
        HttpHeaders mockHeaders = new HttpHeaders();
        mockHeaders.set("authorization", "mockAuthorization");

        when(headersParser.loadHeaderConfiguration()).thenReturn(mockHeaders);
        when(getAccountsBL.clientValidation(null, "mockAuthorization")).thenReturn(null);
        when(getAccountsBL.process(any())).thenReturn(List.of());

        // Act & Assert
        mockMvc.perform(get("/accounts").header("Authorization", "mockAuthorization"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.responseStatus").value("OK"));
    }
}
