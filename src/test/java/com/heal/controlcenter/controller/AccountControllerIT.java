package com.heal.controlcenter.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.controlcenter.beans.AccessDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class AccountControllerIT {

    private MockMvc mockMvc;

    private ObjectMapper objectMapper;

    @Mock
    private GetAccountsBL getAccountsBL;

    @Mock
    private JsonFileParser jsonFileParser;

    @InjectMocks
    private AccountController accountController;

    private static final String MOCK_AUTH_TOKEN = "mockAuthorization";

    @BeforeEach
    void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(accountController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void getAllAccounts_WhenSuccess() throws Exception {
        HttpHeaders mockHeaders = new HttpHeaders();
        mockHeaders.set("authorization", MOCK_AUTH_TOKEN);

        UtilityBean<Account> mockUtilityBean = UtilityBean.<Account>builder()
                .authToken(MOCK_AUTH_TOKEN)
                .build();

        AccessDetailsBean mockAccessDetails = new AccessDetailsBean();

        when(jsonFileParser.loadHeaderConfiguration()).thenReturn(mockHeaders);
        when(getAccountsBL.clientValidation(null, MOCK_AUTH_TOKEN)).thenReturn(mockUtilityBean);
        when(getAccountsBL.serverValidation(mockUtilityBean)).thenReturn(mockAccessDetails);
        when(getAccountsBL.process(mockAccessDetails)).thenReturn(createMockAccounts());

        mockMvc.perform(get("/accounts")
                        .header("Authorization", MOCK_AUTH_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(header().string("authorization", MOCK_AUTH_TOKEN))
                .andExpect(jsonPath("$.message").value("Accounts fetched successfully."))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].accountId").value(1))
                .andExpect(jsonPath("$.responseStatus").value("OK"));
    }

    @Test
    void getAllAccounts_WhenMissingAuthorizationHeader_ShouldReturnBadRequest() throws Exception {
        mockMvc.perform(get("/accounts"))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    private List<Account> createMockAccounts() {
        List<Account> accounts = new ArrayList<>();
        accounts.add(Account.builder()
                .accountId(1)
                .accountName("Test Account 1")
                .identifier("test-account-001")
                .status(1)
                .publicKey("mock-public-key-1")
                .privateKey("mock-private-key-1")
                .build());

        accounts.add(Account.builder()
                .accountId(2)
                .accountName("Test Account 2")
                .identifier("test-account-002")
                .status(1)
                .publicKey("mock-public-key-2")
                .privateKey("mock-private-key-2")
                .build());

        return accounts;
    }
}
