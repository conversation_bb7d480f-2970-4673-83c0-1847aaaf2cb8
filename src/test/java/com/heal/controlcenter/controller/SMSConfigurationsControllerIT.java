package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetSMSConfigurationsBL;
import com.heal.controlcenter.businesslogic.PutSMSConfigurationsBL;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMSDetailsPojo;
import com.heal.controlcenter.util.JsonFileParser;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(SMSConfigurationsController.class)
class SMSConfigurationsControllerIT {

    @Autowired
    private MockMvc mockMvc;
    @MockBean
    GetSMSConfigurationsBL getSMSConfigurationsBL;
    @MockBean
    PutSMSConfigurationsBL putSMSConfigurationsBL;
    @MockBean
    JsonFileParser headersParser;

    String[] requestParams = new String[2];
    UtilityBean<Object> getUtilityBean;
    UtilityBean<SMSDetailsPojo> putUtilityBean;

    SMSDetailsPojo smsDetailsPojo = new SMSDetailsPojo();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        getUtilityBean = UtilityBean.builder()
                .accountIdentifier("mockAccountIdentifier")
                .userId("mockUserId")
                .authToken("mockAuthToken")
                .build();

        putUtilityBean = UtilityBean.<SMSDetailsPojo>builder()
                .accountIdentifier("mockAccountIdentifier")
                .userId("mockUserId")
                .authToken("mockAuthToken")
                .pojoObject(smsDetailsPojo)
                .build();
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void getSMSConfigurations_WhenSuccess() throws Exception {
        when(getSMSConfigurationsBL.clientValidation(null, requestParams)).thenReturn(getUtilityBean);
        when(getSMSConfigurationsBL.serverValidation(getUtilityBean)).thenReturn(anyInt());
        when(getSMSConfigurationsBL.process(1)).thenReturn(smsDetailsPojo);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/{identifier}/sms-configurations", "mockIdentifier")
                        .header("authorization", "mockAuthorization"))
                .andExpect(status().isOk())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("OK")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("SMS configurations fetched successfully.")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.OK.value());
    }

    @Test
    void getSMSConfigurations_WhenServerException() throws Exception {
        getUtilityBean = null;
        when(getSMSConfigurationsBL.clientValidation(null, requestParams)).thenReturn(getUtilityBean);
        when(getSMSConfigurationsBL.serverValidation(getUtilityBean)).thenThrow(ServerException.class);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/{identifier}/sms-configurations", "mockIdentifier")
                        .header("authorization", "mockAuthorization"))
                .andExpect(status().isBadRequest())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.aMapWithSize(6)))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }

    @Test
    void putSMSConfigurations_WhenSuccess() throws Exception {
        when(putSMSConfigurationsBL.clientValidation(smsDetailsPojo, requestParams)).thenReturn(putUtilityBean);
        when(putSMSConfigurationsBL.serverValidation(putUtilityBean)).thenReturn(putUtilityBean);
        when(putSMSConfigurationsBL.process(putUtilityBean)).thenReturn(null);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(put("/accounts/{identifier}/sms-configurations", "mockIdentifier")
                        .header("authorization", "mockAuthorization")
                        .content("{}")
                        .accept(MediaType.APPLICATION_JSON)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("OK")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("SMS configurations updated successfully.")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.OK.value());
    }

    @Test
    void putSMSConfigurations_WhenServerException() throws Exception {
        putUtilityBean = null;
        when(putSMSConfigurationsBL.clientValidation(smsDetailsPojo, requestParams)).thenReturn(putUtilityBean);
        when(putSMSConfigurationsBL.serverValidation(putUtilityBean)).thenThrow(ServerException.class);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(put("/accounts/{identifier}/sms-configurations", "mockIdentifier")
                        .header("authorization", "mockAuthorization")
                        .content("{}")
                        .accept(MediaType.APPLICATION_JSON)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.aMapWithSize(6)))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }
}
