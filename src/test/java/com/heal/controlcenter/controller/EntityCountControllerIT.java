package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.InstancesBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetEntityCountBL;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(EntityCountController.class)
class EntityCountControllerIT {

    @Autowired
    private MockMvc mockMvc;
    @MockBean
    GetEntityCountBL getEntityCountBL;
    @MockBean
    JsonFileParser headersParser;

    UtilityBean<InstancesBean> mockUtilityBean = null;
    String[] requestParams = new String[3];
    InstancesBean instancesBean = new InstancesBean();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";
        requestParams[2] = "SERVICE";

        instancesBean.setTypeName("SERVICE");

        mockUtilityBean = UtilityBean.<InstancesBean>builder()
                .accountIdentifier("mockAccountIdentifier")
                .authToken("mockAuthToken")
                .userId("mockUserId")
                .pojoObject(instancesBean)
                .build();
    }

    @AfterEach
    void tearDown() {
        mockUtilityBean = null;
        instancesBean = null;
    }

    @Test
    void getForensicActions_WhenSuccess() throws Exception {
        Map<String, Object> data = new HashMap<>();
        data.put(Constants.TOTAL, 5);
        when(getEntityCountBL.clientValidation(null, requestParams)).thenReturn(mockUtilityBean);
        when(getEntityCountBL.serverValidation(mockUtilityBean)).thenReturn(instancesBean);
        when(getEntityCountBL.process(instancesBean)).thenReturn(data);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/{identifier}/entity-count?type={typeName}", "mockIdentifier", "mockType")
                        .header("authorization", "mockAuthorization"))
                .andExpect(status().isOk())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("OK")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("Entity count fetched successfully.")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.OK.value());
    }

    @Test
    void getForensicActions_WhenServerException() throws Exception {
        mockUtilityBean = null;
        when(getEntityCountBL.clientValidation(null, requestParams)).thenReturn(mockUtilityBean);
        when(getEntityCountBL.serverValidation(mockUtilityBean)).thenThrow(ServerException.class);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/{identifier}/entity-count?type={typeName}", "mockIdentifier", "mockType")
                        .header("authorization", "mockAuthorization"))
                .andExpect(status().isBadRequest())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.aMapWithSize(6)))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }
}
