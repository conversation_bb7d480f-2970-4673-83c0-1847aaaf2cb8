package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.NotificationSettingsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetNotificationSettingsBL;
import com.heal.controlcenter.businesslogic.PutNotificationSettingsBL;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.NotificationSettings;
import com.heal.controlcenter.pojo.NotificationSettingsPojo;
import com.heal.controlcenter.pojo.UserAccountPojo;
import com.heal.controlcenter.util.JsonFileParser;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(NotificationSettingsController.class)
class NotificationSettingsControllerIT {

    @Autowired
    private MockMvc mockMvc;
    @MockBean
    GetNotificationSettingsBL getNotificationSettingsBL;
    @MockBean
    PutNotificationSettingsBL putNotificationSettingsBL;
    @MockBean
    JsonFileParser headersParser;

    String[] requestParams = new String[2];
    UtilityBean<Object> getUtilityBean;
    UtilityBean<List<NotificationSettingsPojo>> putUtilityBean;
    UserAccountPojo userAccountPojo = new UserAccountPojo();
    List<NotificationSettings> notificationSettingsBeans = new ArrayList<>();
    List<NotificationSettingsPojo> notificationSettingsPojos = new ArrayList<>();

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        getUtilityBean = UtilityBean.builder()
                .accountIdentifier("mockAccountIdentifier")
                .userId("mockUserId")
                .authToken("mockAuthToken")
                .build();

        putUtilityBean = UtilityBean.<List<NotificationSettingsPojo>>builder()
                .accountIdentifier("mockAccountIdentifier")
                .userId("mockUserId")
                .authToken("mockAuthToken")
                .pojoObject(notificationSettingsPojos)
                .build();

        userAccountPojo.setUserId("mockUserId");
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void getNotificationSettings_WhenSuccess() throws Exception {
        when(getNotificationSettingsBL.clientValidation(null, requestParams)).thenReturn(getUtilityBean);
        when(getNotificationSettingsBL.serverValidation(getUtilityBean)).thenReturn(userAccountPojo);
        when(getNotificationSettingsBL.process(userAccountPojo)).thenReturn(notificationSettingsBeans);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/{identifier}/notification-settings", "mockIdentifier")
                        .header("authorization", "mockAuthorization"))
                .andExpect(status().isOk())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("OK")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("Notification settings fetched successfully.")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.OK.value());
    }

    @Test
    void getNotificationSettings_WhenServerException() throws Exception {
        getUtilityBean = null;
        when(getNotificationSettingsBL.clientValidation(null, requestParams)).thenReturn(getUtilityBean);
        when(getNotificationSettingsBL.serverValidation(getUtilityBean)).thenThrow(ServerException.class);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/{identifier}/notification-settings", "mockIdentifier")
                        .header("authorization", "mockAuthorization"))
                .andExpect(status().isBadRequest())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.aMapWithSize(6)))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }

    @Test
    void putNotificationSettings_WhenSuccess() throws Exception {
        when(putNotificationSettingsBL.clientValidation(notificationSettingsPojos, requestParams)).thenReturn(putUtilityBean);
        when(putNotificationSettingsBL.serverValidation(putUtilityBean)).thenReturn(putUtilityBean);
        when(putNotificationSettingsBL.process(putUtilityBean)).thenReturn(null);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(put("/accounts/{identifier}/notification-settings", "mockIdentifier")
                        .header("authorization", "mockAuthorization")
                        .content("[" +
                                        "{" +
                                            "\"typeId\" : 292," +
                                            "\"typeName\" : \"NotificationType\"," +
                                            "\"durationInMin\" : 30" +
                                        "}" +
                                "]"
                        )
                        .accept(MediaType.APPLICATION_JSON)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("OK")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("Notification settings updated successfully.")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.OK.value());
    }

    @Test
    void putNotificationSettings_WhenServerException() throws Exception {
        putUtilityBean = null;
        when(putNotificationSettingsBL.clientValidation(notificationSettingsPojos, requestParams)).thenReturn(putUtilityBean);
        when(putNotificationSettingsBL.serverValidation(putUtilityBean)).thenThrow(ServerException.class);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(put("/accounts/{identifier}/notification-settings", "mockIdentifier")
                        .header("authorization", "mockAuthorization")
                        .content("[" +
                                "{" +
                                "\"typeId\" : 292," +
                                "\"typeName\" : \"NotificationType\"," +
                                "\"durationInMin\" : 30" +
                                "}" +
                                "]"
                        )
                        .accept(MediaType.APPLICATION_JSON)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.aMapWithSize(6)))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }
}
