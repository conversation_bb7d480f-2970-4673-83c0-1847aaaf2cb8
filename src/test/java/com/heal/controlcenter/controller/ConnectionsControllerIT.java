package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetConnectionsBL;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.GetConnectionPojo;
import com.heal.controlcenter.util.JsonFileParser;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(ConnectionsController.class)
class ConnectionsControllerIT {

    @Autowired
    private MockMvc mockMvc;
    @MockBean
    GetConnectionsBL getConnectionsBL;
    @MockBean
    JsonFileParser headersParser;

    List<GetConnectionPojo> pojo = new ArrayList<>();
    UtilityBean<Object> mockUtilityBean = null;
    String[] requestParams = new String[2];

    @BeforeEach
    void setUp() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";

        mockUtilityBean = UtilityBean.builder()
                .accountIdentifier("mockAccountIdentifier")
                .authToken("mockAuthToken")
                .userId("mockUserId")
                .build();
    }

    @AfterEach
    void tearDown() {
        pojo = null;
    }

    @Test
    void getForensicActions_WhenSuccess() throws Exception {
        when(getConnectionsBL.clientValidation(null, requestParams)).thenReturn(mockUtilityBean);
        when(getConnectionsBL.serverValidation(mockUtilityBean)).thenReturn(1);
        when(getConnectionsBL.process(anyInt())).thenReturn(pojo);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/{identifier}/connections", "mockIdentifier")
                        .header("authorization", "mockAuthorization"))
                .andExpect(status().isOk())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("OK")))
                .andExpect(jsonPath("$.message", Matchers.equalTo("Connections fetched successfully.")))
                .andExpect(jsonPath("$.data", Matchers.hasSize(0)))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.OK.value());
    }

    @Test
    void getForensicActions_WhenServerException() throws Exception {
        mockUtilityBean = null;
        when(getConnectionsBL.clientValidation(null, requestParams)).thenReturn(mockUtilityBean);
        when(getConnectionsBL.serverValidation(mockUtilityBean)).thenThrow(ServerException.class);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders() {{
            set("authorization", "mockAuthorization");
        }});
        MockHttpServletResponse mockHttpServletResponse = mockMvc.perform(get("/accounts/{identifier}/connections", "mockIdentifier")
                        .header("authorization", "mockAuthorization"))
                .andExpect(status().isBadRequest())
                .andExpect(header().string("authorization", "mockAuthorization"))
                .andExpect(jsonPath("$.responseStatus", Matchers.equalTo("BAD_REQUEST")))
                .andExpect(jsonPath("$.data", Matchers.aMapWithSize(6)))
                .andExpect(jsonPath("$.data", Matchers.hasKey("status")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("type")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("path")))
                .andExpect(jsonPath("$.data", Matchers.hasKey("error")))
                .andReturn()
                .getResponse();
        assertEquals(mockHttpServletResponse.getStatus(), HttpStatus.BAD_REQUEST.value());
    }
}
