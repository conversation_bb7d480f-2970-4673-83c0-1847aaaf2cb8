package com.heal.controlcenter.beans;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceSuppPersistenceConfigurationBean {

    private int id;
    private int serviceId;
    private int accountId;
    private String userDetailsId;
    private String createdTime;
    private String updatedTime;
    private int startCollectionInterval;
    private int endCollectionInterval;
    private boolean lowEnable;
    private int lowPersistence;
    private int lowSuppression;
    private boolean highEnable;
    private int highPersistence;
    private int highSuppression;
    private boolean mediumEnable;
    private int mediumSuppression;
    private int mediumPersistence;
    private String jaegerServiceId;
    private int closingWindow;
    private int maxDataBreaks;
}
