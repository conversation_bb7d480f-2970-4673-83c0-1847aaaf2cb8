package com.heal.controlcenter.beans;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PostAccountBean {
    private Integer id;
    private String identifier;
    private String accountName;
    private String closingWindow;
    private String maxDataBreaks;
    private Integer status;

    private ThresholdSeverityBean thresholdSeverityBean; // One-to-one relationship
    private List<TagBean> tagBean;                             // One-to-one relationship

    private String publicKey;
    private String privateKey;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
}
