package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetServiceSuppPersistenceBL;
import com.heal.controlcenter.businesslogic.UpdateServiceSuppPersistenceBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AccountServiceKey;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ServiceSuppPersistenceConfigPojo;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@Configuration
@RestController
public class ServiceConfigurationController {


    @Autowired
    UpdateServiceSuppPersistenceBL updateServiceSuppPersistenceBL;

    @Autowired
    GetServiceSuppPersistenceBL getServiceSuppPersistenceBL;

    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Updates service-level anomaly suppression and persistence configurations.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Service configuration updated successfully."
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while updating service configuration."
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while updating service configuration."
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token."
                    )
            }
    )
    @PutMapping(value = "/accounts/{accountIdentifier}/services/{serviceIdentifier}/anomaly/configurations")
    public ResponseEntity<ResponsePojo<Object>>  updateServiceLevelSuppPersistenceConfig(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization,
            @Parameter(
                    name = "accountIdentifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,
            @Parameter(
                    name = "serviceIdentifier",
                    description = "Unique identifier of the service",
                    required = true,
                    example = "service-123"
            )
            @PathVariable("serviceIdentifier") String serviceIdentifier,
            @Parameter(
                    description = "Service configuration details for anomaly suppression and persistence",
                    required = true
            )
            @RequestBody Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails)
            throws ClientException, DataProcessingException, ControlCenterException, ServerException {

        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> utilityBean =  updateServiceSuppPersistenceBL.clientValidation(serviceConfigDetails, authorization, accountIdentifier, serviceIdentifier);
        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> accountServiceKey = updateServiceSuppPersistenceBL.serverValidation(utilityBean);
        updateServiceSuppPersistenceBL.process(accountServiceKey);
        ResponsePojo<Object> responseBean = new ResponsePojo<>("Service Configuration updated successfully.", null, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @Operation(
            summary = "Retrieves service-level anomaly suppression and persistence configurations.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Service configurations fetched successfully.",
                            content = @Content(schema = @Schema(implementation = ServiceSuppPersistenceConfigPojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while fetching service configurations."
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while fetching service configurations."
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token."
                    )
            }
    )
    @GetMapping(value = "/accounts/{accountIdentifier}/services/{serviceIdentifier}/anomaly/configurations")
    public ResponseEntity<ResponsePojo<Map<String, ServiceSuppPersistenceConfigPojo>>> getServiceLevelSuppPersistenceConfig(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization,
            @Parameter(
                    name = "accountIdentifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,
            @Parameter(
                    name = "serviceIdentifier",
                    description = "Unique identifier of the service",
                    required = true,
                    example = "service-123"
            )
            @PathVariable("serviceIdentifier") String serviceIdentifier)
            throws ClientException, ControlCenterException, ServerException {

        UtilityBean<String> utilityBean =  getServiceSuppPersistenceBL.clientValidation(null,authorization, accountIdentifier, serviceIdentifier);
        AccountServiceKey accountServiceKey = getServiceSuppPersistenceBL.serverValidation(utilityBean);
        Map<String, ServiceSuppPersistenceConfigPojo> data = getServiceSuppPersistenceBL.process(accountServiceKey);
        ResponsePojo<Map<String, ServiceSuppPersistenceConfigPojo>> responseBean = new ResponsePojo<>("Service Configurations fetched successfully.", data, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
