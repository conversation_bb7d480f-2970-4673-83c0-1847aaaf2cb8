package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetServiceSuppPersistenceBL;
import com.heal.controlcenter.businesslogic.UpdateServiceSuppPersistenceBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AccountServiceKey;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ServiceSuppPersistenceConfigPojo;
import com.heal.controlcenter.util.JsonFileParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@Configuration
@RestController
public class ServiceConfigurationController {


    @Autowired
    UpdateServiceSuppPersistenceBL updateServiceSuppPersistenceBL;

    @Autowired
    GetServiceSuppPersistenceBL getServiceSuppPersistenceBL;

    @Autowired
    JsonFileParser headersParser;

    @PutMapping(value = "/accounts/{accountIdentifier}/services/{serviceIdentifier}/anomaly/configurations")
    public ResponseEntity<ResponsePojo<Object>>  updateServiceLevelSuppPersistenceConfig(@RequestHeader(value = "Authorization") String authorization,
                                                                                         @PathVariable("accountIdentifier") String accountIdentifier,
                                                                                         @PathVariable("serviceIdentifier") String serviceIdentifier,
                                                                                         @RequestBody Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails)
            throws ClientException, DataProcessingException, ControlCenterException, ServerException {

        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> utilityBean =  updateServiceSuppPersistenceBL.clientValidation(serviceConfigDetails, authorization, accountIdentifier, serviceIdentifier);
        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> accountServiceKey = updateServiceSuppPersistenceBL.serverValidation(utilityBean);
        updateServiceSuppPersistenceBL.process(accountServiceKey);
        ResponsePojo<Object> responseBean = new ResponsePojo<>("Service Configuration updated successfully.", null, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @GetMapping(value = "/accounts/{accountIdentifier}/services/{serviceIdentifier}/anomaly/configurations")
    public ResponseEntity<ResponsePojo<Map<String, ServiceSuppPersistenceConfigPojo>>> getServiceLevelSuppPersistenceConfig(@RequestHeader(value = "Authorization") String authorization,
                                                                                         @PathVariable("accountIdentifier") String accountIdentifier,
                                                                                         @PathVariable("serviceIdentifier") String serviceIdentifier)
            throws ClientException, ControlCenterException, ServerException {

        UtilityBean<String> utilityBean =  getServiceSuppPersistenceBL.clientValidation(null,authorization, accountIdentifier, serviceIdentifier);
        AccountServiceKey accountServiceKey = getServiceSuppPersistenceBL.serverValidation(utilityBean);
        Map<String, ServiceSuppPersistenceConfigPojo> data = getServiceSuppPersistenceBL.process(accountServiceKey);
        ResponsePojo<Map<String, ServiceSuppPersistenceConfigPojo>> responseBean = new ResponsePojo<>("Service Configurations fetched successfully.", data, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
