package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetServicePageAttributesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ServicePageAttributePojo;
import com.heal.controlcenter.util.JsonFileParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Controller
@Configuration
public class ServicePageAttributesController {

    @Autowired
    GetServicePageAttributesBL getServicePageAttributesBL;
    @Autowired
    JsonFileParser headersParser;

    @RequestMapping(value = "/accounts/{identifier}/services/{serviceId}/attributes", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<ServicePageAttributePojo>>> getServiceAttributes(@RequestHeader("Authorization") String authorization,
                                                                                             @PathVariable("identifier") String accountIdentifier,
                                                                                             @PathVariable("serviceId") String serviceId)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getServiceAttributes");

        UtilityBean<Integer> attributesUtilityBean = getServicePageAttributesBL.clientValidation(null, authorization, accountIdentifier, serviceId);
        Integer srvcId = getServicePageAttributesBL.serverValidation(attributesUtilityBean);
        List<ServicePageAttributePojo> data = getServicePageAttributesBL.process(srvcId);

        ResponsePojo<List<ServicePageAttributePojo>> responsePojo = new ResponsePojo<>("Service page attributes fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
