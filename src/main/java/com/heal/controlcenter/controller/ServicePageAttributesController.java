package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetServicePageAttributesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ServicePageAttributePojo;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Configuration
public class ServicePageAttributesController {

    @Autowired
    GetServicePageAttributesBL getServicePageAttributesBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves service page attributes for the specified account and service.",
            description = "Returns detailed attributes and configuration for service pages, including display properties and metadata.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Service page attributes fetched successfully.",
                            content = @Content(schema = @Schema(implementation = ServicePageAttributePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while fetching service page attributes."
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while fetching service page attributes."
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token."
                    )
            }
    )
    @RequestMapping(value = "/accounts/{identifier}/services/{serviceId}/attributes", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<ServicePageAttributePojo>>> getServiceAttributes(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    name = "serviceId",
                    description = "Unique identifier of the service",
                    required = true,
                    example = "service-123"
            )
            @PathVariable("serviceId") String serviceId)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getServiceAttributes");

        UtilityBean<Integer> attributesUtilityBean = getServicePageAttributesBL.clientValidation(null, authorization, accountIdentifier, serviceId);
        Integer srvcId = getServicePageAttributesBL.serverValidation(attributesUtilityBean);
        List<ServicePageAttributePojo> data = getServicePageAttributesBL.process(srvcId);

        ResponsePojo<List<ServicePageAttributePojo>> responsePojo = new ResponsePojo<>("Service page attributes fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
