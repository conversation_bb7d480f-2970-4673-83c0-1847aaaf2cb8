package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAgentCommandsBL;
import com.heal.controlcenter.businesslogic.PostAgentCommandsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentCommandsPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.JsonFileParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class CommandsController {

    @Autowired
    GetAgentCommandsBL getAgentCommandsBL;
    @Autowired
    PostAgentCommandsBL postAgentCommandsBL;
    @Autowired
    JsonFileParser headersParser;

    @RequestMapping(value = "/accounts/{identifier}/services/{serviceId}/agent-commands", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<AgentCommandsPojo>> getAgentCommands(@RequestHeader("Authorization") String authorization,
                                                                            @PathVariable("identifier") String accountIdentifier,
                                                                            @PathVariable("serviceId") String serviceId,
                                                                            @RequestParam("agentType") String agentType)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getAgentCommands");

        UtilityBean<List<String>> utilityBean = getAgentCommandsBL.clientValidation(null, authorization, accountIdentifier, serviceId, agentType);
        getAgentCommandsBL.serverValidation(utilityBean);
        AgentCommandsPojo data = getAgentCommandsBL.process(utilityBean);

        ResponsePojo<AgentCommandsPojo> responseBean = new ResponsePojo<>("Agent commands fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @RequestMapping(value = "/accounts/{identifier}/services/{serviceId}/agent-commands", method = RequestMethod.POST)
    public ResponseEntity<ResponsePojo<Object>> addAgentCommands(@RequestHeader("Authorization") String authorization,
                                                                 @PathVariable("identifier") String accountIdentifier,
                                                                 @PathVariable("serviceId") String serviceId,
                                                                 @RequestParam("agentType") String agentType,
                                                                 @RequestBody AgentCommandsPojo agentCommandsPojo)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : addAgentCommands");

        UtilityBean<AgentCommandsPojo> utilityBean = postAgentCommandsBL.clientValidation(agentCommandsPojo, authorization, accountIdentifier, serviceId, agentType);
        postAgentCommandsBL.serverValidation(utilityBean);
        postAgentCommandsBL.process(utilityBean);

        ResponsePojo<Object> responseBean = new ResponsePojo<>("Agent commands added successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
