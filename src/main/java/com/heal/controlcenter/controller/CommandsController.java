package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAgentCommandsBL;
import com.heal.controlcenter.businesslogic.PostAgentCommandsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentCommandsPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class CommandsController {

    @Autowired
    GetAgentCommandsBL getAgentCommandsBL;
    @Autowired
    PostAgentCommandsBL postAgentCommandsBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves agent commands for the specified account, service, and agent type.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Agent commands fetched successfully.",
                            content = @Content(schema = @Schema(implementation = AgentCommandsPojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while fetching agent commands."
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while fetching agent commands."
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token."
                    )
            }
    )
    @RequestMapping(value = "/accounts/{identifier}/services/{serviceId}/agent-commands", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<AgentCommandsPojo>> getAgentCommands(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    name = "serviceId",
                    description = "Unique identifier of the service",
                    required = true,
                    example = "service-123"
            )
            @PathVariable("serviceId") String serviceId,
            @Parameter(
                    name = "agentType",
                    description = "Type of agent for which to retrieve commands",
                    required = true,
                    example = "JAVA_AGENT"
            )
            @RequestParam("agentType") String agentType)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getAgentCommands");

        UtilityBean<List<String>> utilityBean = getAgentCommandsBL.clientValidation(null, authorization, accountIdentifier, serviceId, agentType);
        getAgentCommandsBL.serverValidation(utilityBean);
        AgentCommandsPojo data = getAgentCommandsBL.process(utilityBean);

        ResponsePojo<AgentCommandsPojo> responseBean = new ResponsePojo<>("Agent commands fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @Operation(
            summary = "Adds agent commands for the specified account, service, and agent type.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Agent commands added successfully."
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while adding agent commands."
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while adding agent commands."
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token."
                    )
            }
    )
    @RequestMapping(value = "/accounts/{identifier}/services/{serviceId}/agent-commands", method = RequestMethod.POST)
    public ResponseEntity<ResponsePojo<Object>> addAgentCommands(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    name = "serviceId",
                    description = "Unique identifier of the service",
                    required = true,
                    example = "service-123"
            )
            @PathVariable("serviceId") String serviceId,
            @Parameter(
                    name = "agentType",
                    description = "Type of agent for which to add commands",
                    required = true,
                    example = "JAVA_AGENT"
            )
            @RequestParam("agentType") String agentType,
            @Parameter(
                    description = "Agent commands configuration to add",
                    required = true
            )
            @RequestBody AgentCommandsPojo agentCommandsPojo)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : addAgentCommands");

        UtilityBean<AgentCommandsPojo> utilityBean = postAgentCommandsBL.clientValidation(agentCommandsPojo, authorization, accountIdentifier, serviceId, agentType);
        postAgentCommandsBL.serverValidation(utilityBean);
        postAgentCommandsBL.process(utilityBean);

        ResponsePojo<Object> responseBean = new ResponsePojo<>("Agent commands added successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
