package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.AccessDetailsBean;
import com.heal.controlcenter.beans.PostAccountBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.businesslogic.PostAccountService;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class AccountController {

    @Autowired
    GetAccountsBL getAccountsBL;

    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves all active accounts for the authenticated user.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Accounts fetched successfully.",
                            content = @Content(schema = @Schema(implementation = Account.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while fetching accounts."
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while fetching accounts."
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token."
                    )
            }
    )
    @GetMapping(value = "/accounts")
    public ResponseEntity<ResponsePojo<List<Account>>> getAllAccounts(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization)
            throws DataProcessingException, ControlCenterException, ClientException, ServerException {

        UtilityBean<Account> utilityBean =getAccountsBL.clientValidation(null,authorization);
        AccessDetailsBean accessDetailsBean =getAccountsBL.serverValidation(utilityBean);
        List<Account> data = getAccountsBL.process(accessDetailsBean);

        ResponsePojo<List<Account>> responseBean = new ResponsePojo<>("Accounts fetched successfully.", data, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}