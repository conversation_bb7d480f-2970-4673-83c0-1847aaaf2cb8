package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.PostAccountBean;
import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.businesslogic.PostAccountService;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.AccountRequest;
import com.heal.controlcenter.pojo.AccountResponse;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.JsonFileParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class AccountController {

    @Autowired
    GetAccountsBL getAccountsBL;

    @Autowired
    JsonFileParser headersParser;

    @Autowired
    PostAccountService postAccountService;

    @RequestMapping(value = "/accounts", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<Account>>> getAllAccounts(@RequestHeader("Authorization") String authorization)
            throws DataProcessingException, ControlCenterException, ClientException, ServerException {

        getAccountsBL.clientValidation(null, authorization);
        List<Account> data = getAccountsBL.process("accounts");

        ResponsePojo<List<Account>> responseBean = new ResponsePojo<>("Accounts fetched successfully.", data, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @PostMapping("/accounts")
    public ResponseEntity<AccountResponse> createAccount(@RequestBody AccountRequest request) {
        try {
            int accountId = postAccountService.createAccount(request);
            PostAccountBean postAccountBean = postAccountService.getAccountById(accountId);
            AccountResponse response = postAccountService.buildResponse(postAccountBean);
            return new ResponseEntity<>(response, HttpStatus.CREATED);
        } catch (ControlCenterException e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}