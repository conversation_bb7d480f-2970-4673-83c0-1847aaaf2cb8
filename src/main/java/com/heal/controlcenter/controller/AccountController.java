package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.AccessDetailsBean;
import com.heal.controlcenter.beans.PostAccountBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.businesslogic.PostAccountService;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.JsonFileParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class AccountController {

    @Autowired
    GetAccountsBL getAccountsBL;

    @Autowired
    JsonFileParser headersParser;

    @GetMapping(value = "/accounts")
    public ResponseEntity<ResponsePojo<List<Account>>> getAllAccounts( @RequestHeader("Authorization") String authorization)
            throws DataProcessingException, ControlCenterException, ClientException, ServerException {

        UtilityBean<Account> utilityBean =getAccountsBL.clientValidation(null,authorization);
        AccessDetailsBean accessDetailsBean =getAccountsBL.serverValidation(utilityBean);
        List<Account> data = getAccountsBL.process(accessDetailsBean);

        ResponsePojo<List<Account>> responseBean = new ResponsePojo<>("Accounts fetched successfully.", data, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}