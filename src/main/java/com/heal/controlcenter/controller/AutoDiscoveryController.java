package com.heal.controlcenter.controller;

import com.appnomic.appsone.common.beans.discovery.Component;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAutoDiscoveryIgnoredEntitiesBL;
import com.heal.controlcenter.businesslogic.GetAutoDiscoveryKnownComponentsBL;
import com.heal.controlcenter.businesslogic.PostAutoDiscoveryMapToServiceBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AutoDiscoveryIgnoredEntitiesPojo;
import com.heal.controlcenter.pojo.AutoDiscoveryMapEntityPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.JsonFileParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Configuration
@RestController
public class AutoDiscoveryController {

    @Autowired
    PostAutoDiscoveryMapToServiceBL postAutoDiscoveryMapToServiceBL;
    @Autowired
    GetAutoDiscoveryIgnoredEntitiesBL getAutoDiscoveryIgnoredEntitiesBL;
    @Autowired
    GetAutoDiscoveryKnownComponentsBL getAutoDiscoveryKnownComponentsBL;
    @Autowired
    JsonFileParser headersParser;

    @RequestMapping(value = "/accounts/{identifier}/ignored-entities", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<AutoDiscoveryIgnoredEntitiesPojo>>> getIgnoredEntities(@RequestHeader("Authorization") String authorization,
                                                                                                   @PathVariable("identifier") String accountIdentifier)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getIgnoredEntities");
        Object bean = new Object();

        UtilityBean<Object> utilityBean = getAutoDiscoveryIgnoredEntitiesBL.clientValidation(null, authorization, accountIdentifier);
        getAutoDiscoveryIgnoredEntitiesBL.serverValidation(utilityBean);
        List<AutoDiscoveryIgnoredEntitiesPojo> data = getAutoDiscoveryIgnoredEntitiesBL.process(bean);

        ResponsePojo<List<AutoDiscoveryIgnoredEntitiesPojo>> responseBean = new ResponsePojo<>("Ignored entities fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @RequestMapping(value = "/accounts/{identifier}/map-to-service", method = RequestMethod.POST)
    public ResponseEntity<ResponsePojo<String>> mapEntity(@RequestHeader("Authorization") String authorization,
                                                          @PathVariable("identifier") String accountIdentifier,
                                                          @Validated @RequestBody AutoDiscoveryMapEntityPojo body)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : mapEntity");

        UtilityBean<AutoDiscoveryMapEntityPojo> utilityBean = postAutoDiscoveryMapToServiceBL.clientValidation(body, authorization, accountIdentifier);
        AutoDiscoveryMapEntityPojo mapEntityBean = postAutoDiscoveryMapToServiceBL.serverValidation(utilityBean);
        List<String> invalidJsonObjects = postAutoDiscoveryMapToServiceBL.process(mapEntityBean);

        ResponsePojo<String> responseBean = new ResponsePojo<>();
        if (invalidJsonObjects.contains("UNMAPPING")) {
            invalidJsonObjects.remove("UNMAPPING");
            if (invalidJsonObjects.size() == 0) {
                responseBean.setData(null);
                responseBean.setMessage("Service(s) unmapped successfully.");
                responseBean.setResponseStatus(HttpStatus.OK);

            } else if (invalidJsonObjects.size() < Arrays.asList(mapEntityBean.getServiceMappingIdentifiers()).size()) {
                responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseBean.setMessage("Some invalid entities found.");
                responseBean.setResponseStatus(HttpStatus.MULTI_STATUS);

            } else if (invalidJsonObjects.size() == Arrays.asList(mapEntityBean.getServiceMappingIdentifiers()).size()) {
                responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseBean.setMessage("Invalid entities found.");
                responseBean.setResponseStatus(HttpStatus.BAD_REQUEST);
            }
        } else if (invalidJsonObjects.contains("WRONG-EDIT")) {
            invalidJsonObjects.remove("WRONG-EDIT");
            responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
            responseBean.setMessage("Cannot edit component instance mappings here!");
            responseBean.setResponseStatus(HttpStatus.BAD_REQUEST);
        } else if (invalidJsonObjects.contains("PASS")) {
            invalidJsonObjects.remove("PASS");
            if (invalidJsonObjects.size() == 0) {
                responseBean.setData(null);
                responseBean.setMessage("Mapping successful and related connections are discovered.");
                responseBean.setResponseStatus(HttpStatus.OK);
            }
        }
        if (invalidJsonObjects.stream().noneMatch(i -> i.equals("UNMAPPING") || i.equals("WRONG-EDIT"))) {
            if (invalidJsonObjects.size() > 0 && invalidJsonObjects.size() < Arrays.asList(mapEntityBean.getServiceMappingIdentifiers()).size()) {
                responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseBean.setMessage("Some invalid entities found.");
                responseBean.setResponseStatus(HttpStatus.MULTI_STATUS);
            } else if (invalidJsonObjects.size() == Arrays.asList(mapEntityBean.getServiceMappingIdentifiers()).size()) {
                responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseBean.setMessage("Invalid entities found.");
                responseBean.setResponseStatus(HttpStatus.BAD_REQUEST);
            }
        }

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @RequestMapping(value = "/auto-discovery-components", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<Component>>> getComponentAttributeInfo()
            throws DataProcessingException {
        log.trace("Method Invoked : getComponentAttributeInfo");

        List<Component> data = getAutoDiscoveryKnownComponentsBL.process(new Object());

        ResponsePojo<List<Component>> responsePojo = new ResponsePojo<>("Components info fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
