package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetConnectionsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.GetConnectionPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.JsonFileParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class ConnectionsController {

    @Autowired
    GetConnectionsBL getConnectionsBL;
    @Autowired
    JsonFileParser headersParser;

    @RequestMapping(value = "/accounts/{identifier}/connections", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<GetConnectionPojo>>> getConnections(@RequestHeader("Authorization") String authorization,
                                                                                @PathVariable("identifier") String accountIdentifier)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getConnections");

        UtilityBean<Object> utilityBean = getConnectionsBL.clientValidation(null, authorization, accountIdentifier);
        Integer accountId = getConnectionsBL.serverValidation(utilityBean);
        List<GetConnectionPojo> data = getConnectionsBL.process(accountId);

        ResponsePojo<List<GetConnectionPojo>> responsePojo = new ResponsePojo<>("Connections fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
