package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.businesslogic.AddApplicationsBL;
import com.heal.controlcenter.businesslogic.DeleteApplicationsBL;
import com.heal.controlcenter.businesslogic.GetApplicationsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Application;
import com.heal.controlcenter.pojo.GetApplications;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.UserAccessInfo;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;


@Slf4j
@RestController
@Configuration
public class ApplicationsController {

    @Autowired
    GetApplicationsBL getApplicationsBL;
    @Autowired
    AddApplicationsBL addApplicationsBL;
    @Autowired
    DeleteApplicationsBL deleteApplicationsBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves applications for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Applications fetched successfully.",
                            content = @Content(schema = @Schema(implementation = GetApplications.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while fetching applications."
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while fetching applications."
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token."
                    )
            }
    )
    @RequestMapping(value = "accounts/{identifier}/applications", method = RequestMethod.GET)
    public ResponseEntity<Object> applications(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable(value = "identifier") String accountIdentifier,
            @Parameter(
                    name = "clusterDataRequired",
                    description = "Whether to include cluster data in the response",
                    required = false,
                    example = "true"
            )
            @RequestParam(value = "clusterDataRequired", required = false, defaultValue = "true") String clusterDataRequired)
            throws ClientException, DataProcessingException, ServerException, ControlCenterException {

        UtilityBean<String> applicationBean = getApplicationsBL.clientValidation(null, authorization, accountIdentifier, clusterDataRequired);
        UserAccessInfo userAccessInfo = getApplicationsBL.serverValidation(applicationBean);
        List<GetApplications> listOfApplications = getApplicationsBL.process(userAccessInfo);

        ResponsePojo<List<GetApplications>> responsePojo = new ResponsePojo<>("Applications fetching successfully",
                listOfApplications, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Adds a new application for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Application added successfully.",
                            content = @Content(schema = @Schema(implementation = IdPojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while adding application."
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while adding application."
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token."
                    )
            }
    )
    @RequestMapping(value = "accounts/{identifier}/applications", method = RequestMethod.POST)
    public ResponseEntity<Object> addApplications(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable(value = "identifier") String accountIdentifier,
            @Parameter(
                    description = "Application details to add",
                    required = true
            )
            @Valid @RequestBody Application body)
            throws ClientException, ServerException, DataProcessingException {

        UtilityBean<Application> applicationBean = addApplicationsBL.clientValidation(body, authorization, accountIdentifier);
        ApplicationBean bean = addApplicationsBL.serverValidation(applicationBean);
        IdPojo idPojo = addApplicationsBL.process(bean);

        ResponsePojo<IdPojo> responsePojo = new ResponsePojo<>("Applications added successfully", idPojo, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Deletes applications for the specified account.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Applications removed successfully."
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while deleting applications."
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while deleting applications."
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token."
                    )
            }
    )
    @RequestMapping(method = RequestMethod.DELETE, value = "accounts/{identifier}/applications")
    public ResponseEntity<?> deleteApplications(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization,
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable(value = "identifier") String accountIdentifier,
            @Parameter(
                    name = "appIdentifiers",
                    description = "Array of application identifiers to delete",
                    required = true,
                    example = "app-001,app-002,app-003"
            )
            @RequestParam(value = "appIdentifiers") String[] appIdentifiers)
            throws DataProcessingException, ClientException, ServerException {

        UtilityBean<List<String>> applicationList = deleteApplicationsBL.clientValidation(appIdentifiers, authorization, accountIdentifier);
        List<ControllerBean> controllerBeanList = deleteApplicationsBL.serverValidation(applicationList);
        deleteApplicationsBL.process(controllerBeanList);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body("Applications removed successfully");
    }
}
