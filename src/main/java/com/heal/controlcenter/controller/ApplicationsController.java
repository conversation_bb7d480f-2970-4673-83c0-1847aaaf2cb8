package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.businesslogic.AddApplicationsBL;
import com.heal.controlcenter.businesslogic.DeleteApplicationsBL;
import com.heal.controlcenter.businesslogic.GetApplicationsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Application;
import com.heal.controlcenter.pojo.GetApplications;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.UserAccessInfo;
import com.heal.controlcenter.util.JsonFileParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;


@Slf4j
@RestController
@Configuration
public class ApplicationsController {

    @Autowired
    GetApplicationsBL getApplicationsBL;
    @Autowired
    AddApplicationsBL addApplicationsBL;
    @Autowired
    DeleteApplicationsBL deleteApplicationsBL;
    @Autowired
    JsonFileParser headersParser;

    @RequestMapping(value = "accounts/{identifier}/applications", method = RequestMethod.GET)
    public ResponseEntity<Object> applications(@RequestHeader(value = "Authorization") String authorization, @PathVariable(value = "identifier") String accountIdentifier,
                                               @RequestParam(value = "clusterDataRequired", required = false, defaultValue = "true") String clusterDataRequired)
            throws ClientException, DataProcessingException, ServerException, ControlCenterException {

        UtilityBean<String> applicationBean = getApplicationsBL.clientValidation(null, authorization, accountIdentifier, clusterDataRequired);
        UserAccessInfo userAccessInfo = getApplicationsBL.serverValidation(applicationBean);
        List<GetApplications> listOfApplications = getApplicationsBL.process(userAccessInfo);

        ResponsePojo<List<GetApplications>> responsePojo = new ResponsePojo<>("Applications fetching successfully",
                listOfApplications, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @RequestMapping(value = "accounts/{identifier}/applications", method = RequestMethod.POST)
    public ResponseEntity<Object> addApplications(@RequestHeader(value = "Authorization") String authorization,
                                                  @PathVariable(value = "identifier") String accountIdentifier,
                                                  @Valid @RequestBody Application body)
            throws ClientException, ServerException, DataProcessingException {

        UtilityBean<Application> applicationBean = addApplicationsBL.clientValidation(body, authorization, accountIdentifier);
        ApplicationBean bean = addApplicationsBL.serverValidation(applicationBean);
        IdPojo idPojo = addApplicationsBL.process(bean);

        ResponsePojo<IdPojo> responsePojo = new ResponsePojo<>("Applications added successfully", idPojo, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = "accounts/{identifier}/applications")
    public ResponseEntity<?> deleteApplications(@RequestHeader(value = "Authorization") String authorization,
                                                @PathVariable(value = "identifier") String accountIdentifier,
                                                @RequestParam(value = "appIdentifiers") String[] appIdentifiers)
            throws DataProcessingException, ClientException, ServerException {

        UtilityBean<List<String>> applicationList = deleteApplicationsBL.clientValidation(appIdentifiers, authorization, accountIdentifier);
        List<ControllerBean> controllerBeanList = deleteApplicationsBL.serverValidation(applicationList);
        deleteApplicationsBL.process(controllerBeanList);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body("Applications removed successfully");
    }
}
