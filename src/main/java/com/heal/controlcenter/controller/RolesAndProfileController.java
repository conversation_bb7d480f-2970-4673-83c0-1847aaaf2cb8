package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.IdBean;
import com.heal.controlcenter.businesslogic.UserProfilesByRoleIdBL;
import com.heal.controlcenter.businesslogic.UserRoleBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.JsonFileParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@Slf4j
@Controller
@Configuration
public class RolesAndProfileController {

    @Autowired
    UserRoleBL userRoleBL;
    @Autowired
    JsonFileParser headersParser;
    @Autowired
    UserProfilesByRoleIdBL userProfilesByRoleIdBL;

    @RequestMapping(value = "/roles", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<IdBean>>> getUserRoles(@RequestHeader(value = "Authorization") String authorization)
            throws ClientException, DataProcessingException {
        userRoleBL.clientValidation(null, authorization);
        List<IdBean> listOfUserRoles = userRoleBL.process("User roles");
        ResponsePojo<List<IdBean>> responsePojo = new ResponsePojo<>("User roles fetched successfully", listOfUserRoles, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @RequestMapping(value = "/roles/{roleId}/profiles", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<IdBean>>> getProfilesByRoleId(@RequestHeader(value = "Authorization") String authorization,
                                                                          @PathVariable(value = "roleId") Long id)
            throws ClientException, DataProcessingException {
        userProfilesByRoleIdBL.clientValidation(authorization);
        List<IdBean> listOfProfilesByRoleId = userProfilesByRoleIdBL.process(id);
        ResponsePojo<List<IdBean>> responseBean = new ResponsePojo<>("User profiles fetched successfully", listOfProfilesByRoleId, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
