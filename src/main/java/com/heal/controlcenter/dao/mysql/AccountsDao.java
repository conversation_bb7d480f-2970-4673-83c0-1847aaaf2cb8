package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.pojo.AccountRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

@Slf4j
@Repository
public class AccountsDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public List<AccountBean> getAccounts() throws ControlCenterException {
        String query = "select a.identifier identifier, a.id id, a.name name, a.public_key publicKey, a.private_key privateKey, " +
                "a.user_details_id userIdDetails, a.status status, a.updated_time updatedTime, a.created_time createdTime " +
                "from account a where a.status = 1";
        try {
            log.debug("getting accounts list");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(AccountBean.class));
        } catch (Exception ex) {
            log.error("Error in fetching accounts", ex);
            throw new ControlCenterException("Error in fetching accounts");
        }
    }

    public AccountBean getAccountDetailsForIdentifier(String accountIdentifier) {
        String query = "select a.id as id, a.status, a.name, a.identifier, a.user_details_id as userDetailsId " +
                "FROM account a where a.identifier=?";

        try {
            return jdbcTemplate.queryForObject(query, BeanPropertyRowMapper.newInstance(AccountBean.class), accountIdentifier);
        } catch (Exception e) {
            log.error("Exception encountered while fetching accounts information. Details: ", e);
        }

        return null;
    }

    public UserAccessBean fetchUserAccessDetailsUsingIdentifier(String userIdentifier) {
        try {
            String query = "select a.access_details, a.user_identifier from user_access_details a where user_identifier=?";
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(UserAccessBean.class),
                    userIdentifier);
        } catch (DataAccessException e) {
            log.error("Error while fetching user access information for user [{}]. Details: ", userIdentifier, e);
        }

        return null;
    }

    public AccountBean getAccountByIdentifier(String identifier) {
        String query = "select identifier, id, name, public_key publicKey, private_key privateKey, " +
                "user_details_id lastModifiedBy, status, updated_time updatedTime, created_time createdTime " +
                "from account where status = 1 and identifier=?";
        try {
            log.debug("getting account details.");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(AccountBean.class), identifier);
        } catch (Exception e) {
            log.error("Error occurred while fetching account details from 'account' table for identifier [{}]. Details: {}, Stack trace: {}", identifier, e.getMessage(), e.getStackTrace());
        }
        return null;
    }

    public TimezoneBean getAccountTimezoneDetails(int accountId) throws ControlCenterException {
        try {
            String QUERY_BY_ACCOUNT_ID = "SELECT id, time_zone_id AS timeZoneId, timeoffset AS offset, " +
                    "account_id AS accountId" +
                    "FROM tag_mapping tm, mst_timezone mt, tag_details td " +
                    "WHERE tm.object_id = ? and tm.object_ref_table = 'account' and tm.tag_id = td.id " +
                    "and td.name = 'Timezone' and mt.id = tm.tag_key";
            return jdbcTemplate.queryForObject(QUERY_BY_ACCOUNT_ID,
                    new BeanPropertyRowMapper<>(TimezoneBean.class),accountId);
        } catch (Exception e) {
            log.error("Error while getting time zone for accountId {}. Details: {}, Stack trace: {}",
                    accountId, e.getMessage(), e.getStackTrace());
            throw new ControlCenterException("Error in fetching timezones for account ID: " + accountId);
        }
    }

    public int insertAccount(AccountRequest req, String publicKey, String privateKey, String updatedBy, String updatedTime) {
        String query = "INSERT INTO account_1 (identifier, account_name, closing_window, max_data_breaks, status, public_key, private_key, updated_by, updated_time) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        jdbcTemplate.update(query,
                req.getIdentifier(),
                req.getAccountName(),
                req.getClosingWindow(),
                req.getMaxDataBreaks(),
                req.getStatus(),
                publicKey,
                privateKey,
                updatedBy,
                updatedTime);
        return jdbcTemplate.queryForObject("SELECT LAST_INSERT_ID()", Integer.class);
    }

    public int insertThresholdSeverity(ThresholdSeverityBean ts, int accountId) {
        String query = "INSERT INTO threshold_severity1 (low, warning, critical, account_id) VALUES (?, ?, ?, ?)";
        jdbcTemplate.update(query, ts.isLow(), ts.isWarning(), ts.isCritical(), accountId);
        return jdbcTemplate.queryForObject("SELECT LAST_INSERT_ID()", Integer.class);
    }

    public int insertTag(TagBean tagBean, int accountId) {
        String query = "INSERT INTO tag_1 (account_id, identifier,name) VALUES (?, ?, ?)";
        jdbcTemplate.update(query, accountId, tagBean.getIdentifier(), tagBean.getName());
        return jdbcTemplate.queryForObject("SELECT LAST_INSERT_ID()", Integer.class);
    }

    public PostAccountBean getAccountById(int accountId) {
        // Get account basic data + threshold severity
        String sql = "SELECT a.id, a.identifier, a.account_name, a.closing_window, a.max_data_breaks, a.status, " +
                "ts.id as severity_id, ts.low, ts.warning, ts.critical " +
                "FROM account_1 a " +
                "JOIN threshold_severity1 ts ON ts.account_id = a.id " +
                "WHERE a.id = ?";

        PostAccountBean postAccountBean = jdbcTemplate.queryForObject(sql, (rs, rowNum) -> {
            ThresholdSeverityBean ts = new ThresholdSeverityBean();
            ts.setId(rs.getInt("severity_id"));
            ts.setLow(rs.getBoolean("low"));
            ts.setWarning(rs.getBoolean("warning"));
            ts.setCritical(rs.getBoolean("critical"));

            PostAccountBean acc = new PostAccountBean();
            acc.setId(rs.getInt("id"));
            acc.setIdentifier(rs.getString("identifier"));
            acc.setAccountName(rs.getString("account_name"));
            acc.setClosingWindow(rs.getString("closing_window"));
            acc.setMaxDataBreaks(rs.getString("max_data_breaks"));
            acc.setStatus(rs.getInt("status"));
            acc.setThresholdSeverityBean(ts);
            return acc;
        }, accountId);

        // Fetch associated tags separately
        String tagSql = "SELECT id, name, identifier FROM tag_1 WHERE account_id = ?";
        List<TagBean> tagBeans = jdbcTemplate.query(tagSql, (rs, rowNum) -> {
            TagBean tagBean = new TagBean();
            tagBean.setId(rs.getInt("id"));
            tagBean.setName(rs.getString("name"));
            tagBean.setIdentifier(rs.getString("identifier"));
            return tagBean;
        }, accountId);

        postAccountBean.setTagBean(tagBeans); // assuming Account has `List<Tag> tag` + corresponding setter
        return postAccountBean;
    }
}
