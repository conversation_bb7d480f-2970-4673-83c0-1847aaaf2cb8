package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

@Slf4j
public class AccountRepo {

    @Autowired
    RedisUtilities redisUtilities;

    public List<Account> getAccounts() {
        String ACCOUNTS_KEY = "/accounts";
        String ACCOUNTS_HASH = "ACCOUNT_DATA";
        try {
            String accountBean = redisUtilities.getKey(ACCOUNTS_KEY, ACCOUNTS_HASH);
            if (accountBean == null) {
                log.debug("Account details not found");
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(accountBean, new TypeReference<List<Account>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting account Details", e);
            return Collections.emptyList();
        }
    }
}
