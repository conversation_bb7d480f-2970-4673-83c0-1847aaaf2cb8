package com.heal.controlcenter.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RedisUtilities {

    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    public RedisTemplate<String, Object> redisTemplate;

    public String getKey(String key, String hashKey) throws Exception {
        try {
            log.trace("Redis GET Query details:- Key:{}, Hash key:{}", key, hashKey);

            Object data = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (data == null) {
                log.trace("Failed to get details for Redis Key:{}, Hash key:{}", key, hashKey);
                return null;
            } else {
                log.trace("Redis GET Query successful. Key:{}, Hash key:{}, Value: {}", key, hashKey, data);
            }
            return (String) data;
        } catch (Exception ex) {
            log.error("Exception encountered while getting value of Redis Key:{}, Hash key:{}. ", key, hashKey, ex);
            throw new Exception(ex);
        }
    }

    public void updateKey(String key, String hashKey, Object data) {
        try {
            if (data == null || objectMapper.writeValueAsString(data).trim().equalsIgnoreCase("")) {
                log.error("Value received to update Redis Key:{}, Hash key:{} is null/empty.", key, hashKey);
                return;
            }

            log.trace("Redis UPDATE Query details:- Key:{}, Hash key:{}, Value: {}", key, hashKey, data);

            String cacheData = getKey(key, hashKey);
            if (cacheData != null && cacheData.hashCode() == objectMapper.writeValueAsString(data).hashCode()) {
                log.debug("No change in value for the Redis Key:{}, Hash key:{}, existing value:{}, new value:{}",
                        key, hashKey, cacheData, data);
                return;
            }

            redisTemplate.opsForHash().put(key, hashKey, objectMapper.writeValueAsString(data));
        } catch (Exception ex) {
            log.error("Exception encountered while updating Redis Key:{}, Hash key:{} with Value:{}. ", key, hashKey, data, ex);
        }
    }

    public void deleteKey(String key, String hashKey) {
        try {
            long deletedKeyCount = redisTemplate.opsForHash().delete(key, hashKey);
            if (deletedKeyCount <= 0) {
                log.warn("Couldn't delete key {}.", key);
            }
        } catch (Exception ex) {
            log.error("Exception encountered while deleting Redis Key:{}, Hash key:{}. ", key, hashKey, ex);
        }
    }

}
