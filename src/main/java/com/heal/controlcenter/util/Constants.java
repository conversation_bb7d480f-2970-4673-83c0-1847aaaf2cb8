package com.heal.controlcenter.util;

public class Constants {

    public static final String TOTAL="total";

    // General
    public static final String DATE_TIME="yyyy-MM-dd HH:mm:ss";

    // ECDSA constants
    public static final String BC_PROVIDER_NAME = "BC";

    /**
     * Notification constants
     */
    public static final String NOTIFICATION_TYPE_LITERAL = "NotificationType";
    public static final String LONG = "Open for long";
    public static final String TOO_LONG = "Open for too long";

    // SMS and SMTP
    public static final String SMS_ACTION_ADD = "add";
    public static final String SMS_ACTION_EDIT = "edit";
    public static final String SMS_ACTION_DELETE = "delete";
    public static final String SMS_PROTOCOLS = "SMSGatewayProtocols";
    public static final String SMS_HTTP_METHODS= "HTTPSMSRequestMethods";
    public static final String SMS_PARAMETER_TYPE_NAME= "SMSParameterTypes";
    public static final String SMS_PLACEHOLDERS= "SMSPlaceHolders";
    public static final String SMTP_PROTOCOLS = "SMTP Security";


    public static final String CONTROLLER = "controller";
    public static final String SERVICES_LAYER_TYPE = "ServiceLayers";

    public static final String TIME_ZONE_TAG = "Timezone";
    public static final String SIGNAL_SEVERITY_TYPE_LITERAL = "SignalSeverity";
    public static final String SIGNAL_TYPE_LITERAL = "SignalType";
    public static final String SEVERE = "Severe";
    public static final String DEFAULT = "Default";
    public static final String EARLY_WARNING = "Early Warning";
    public static final String PROBLEM = "Problem";
    public static final String INFO = "Info";
    public static final String BATCH = "Batch Job";
    public static final String IMMEDIATELY = "Immediately";
    public static final String APPLICATION_PERCENTILES_DEFAULT_CONFIGURATION = "application.percentiles.default";
    public static final String APPLICATION_PERCENTILES_DEFAULT_VALUES = "50,75,90,95,99";

    // Agent commands
    public static final String MST_TYPE_ATTRIBUTE_TYPE = "Attribute_Type";
    public static final String MST_TYPE_COMMAND_OUTPUT_TYPE = "CommandOutputType";
    public static final String MST_TYPE_CONF_CMDS = "ConfigurationCmds";
    public static final String MST_TYPE_SCRIPT_PARAM_TYPE = "SCRIPT_Parameter_Type";

    public static final String MST_SUB_TYPE_COMMAND_LINE = "COMMANDLINE";
    public static final String MST_SUB_TYPE_BLOB = "Blob";
    public static final String MST_SUB_TYPE_TEXT_BOX = "TextBox";
    public static final String MST_SUB_TYPE_EXECUTE = "Execute";

    // Agent commands
    public static final String AGENT_TYPE = "Agent";
    public static final String JIM_AGENT_SUB_TYPE ="JIMAgent";
    public static final String AGENT_MODE_AUTO = "Auto";
    public static final String AGENT_MODE_VERBOSE = "Verbose";
    public static final int DEFAULT_TIMEOUT_IN_SECS = 300;
    public static final String AGENT_SILENT_WINDOW = "15";

    public static final String SERVICES_CONTROLLER_TYPE = "Services";
    public static final String CONTROLLER_TYPE_NAME_DEFAULT = "ControllerType";

    public static final int AVAILABLE_CORES = Runtime.getRuntime().availableProcessors();

    public static final String WORKER_THREAD_MULTIPLIER_PROPERTY_NAME = "worker.thread.multiplier";
    public static final String WORKER_THREAD_DEFAULT_MULTIPLIER = "1";

    // Tag details
    public static final String CONTROLLER_TAG = "Controller";

    // Table names
    public static final String AGENT_TABLE = "agent";

    // service suppression persistence
    public static final String OPERATOR_LESS_THAN = "lt";
    public static final String OPERATOR_GREATER_THAN = "gte";

    // Default suppression persistence values
    public static final String SERVICE_START_WITHIN_AN_HOUR_PROPERTY_NAME = "service.startTime.lesserThan.hour";
    public static final String SERVICE_END_WITHIN_AN_HOUR_PROPERTY_NAME = "service.endTime.lesserThan.hour";
    public static final String SERVICE_START_TIME_WITHIN_AN_HOUR = "1";
    public static final String SERVICE_END_TIME_WITHIN_AN_HOUR = "59";

    public static final String SERVICE_START_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.startTime.greaterThan.hour";
    public static final String SERVICE_END_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.endTime.greaterThan.hour";
    public static final String SERVICE_START_TIME_MORE_THAN_AN_HOUR = "60";
    public static final String SERVICE_END_TIME_MORE_THAN_AN_HOUR = "1440";

    public static final String SERVICE_LOW_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME = "service.low.persistence.lesserThan.hour";
    public static final String SERVICE_LOW_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME = "service.low.suppression.lesserThan.hour";
    public static final String SERVICE_LOW_PERSISTENCE_WITHIN_AN_HOUR = "5";
    public static final String SERVICE_LOW_SUPPRESSION_WITHIN_AN_HOUR = "10";

    public static final String SERVICE_LOW_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.low.persistence.greaterThan.hour";
    public static final String SERVICE_LOW_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.low.suppression.greaterThan.hour";
    public static final String SERVICE_LOW_PERSISTENCE_MORE_THAN_AN_HOUR = "2";
    public static final String SERVICE_LOW_SUPPRESSION_MORE_THAN_AN_HOUR = "5";

    public static final String SERVICE_MEDIUM_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME = "service.medium.persistence.lesserThan.hour";
    public static final String SERVICE_MEDIUM_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME = "service.medium.suppression.lesserThan.hour";
    public static final String SERVICE_MEDIUM_PERSISTENCE_WITHIN_AN_HOUR = "5";
    public static final String SERVICE_MEDIUM_SUPPRESSION_WITHIN_AN_HOUR = "10";

    public static final String SERVICE_MEDIUM_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.medium.persistence.greaterThan.hour";
    public static final String SERVICE_MEDIUM_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.medium.suppression.greaterThan.hour";
    public static final String SERVICE_MEDIUM_PERSISTENCE_MORE_THAN_AN_HOUR = "2";
    public static final String SERVICE_MEDIUM_SUPPRESSION_MORE_THAN_AN_HOUR = "5";

    public static final String SERVICE_HIGH_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME = "service.high.persistence.lesserThan.hour";
    public static final String SERVICE_HIGH_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME = "service.high.suppression.lesserThan.hour";
    public static final String SERVICE_HIGH_PERSISTENCE_WITHIN_AN_HOUR = "5";
    public static final String SERVICE_HIGH_SUPPRESSION_WITHIN_AN_HOUR = "10";

    public static final String SERVICE_HIGH_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.high.persistence.greaterThan.hour";
    public static final String SERVICE_HIGH_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.high.suppression.greaterThan.hour";
    public static final String SERVICE_HIGH_PERSISTENCE_MORE_THAN_AN_HOUR = "2";
    public static final String SERVICE_HIGH_SUPPRESSION_MORE_THAN_AN_HOUR = "5";

    public static final String SERVICE_HIGH_SUPPRESSION_PERSISTENCE_ENABLE = "service.high.suppression.persistence.enable";
    public static final String SERVICE_MEDIUM_SUPPRESSION_PERSISTENCE_ENABLE = "service.medium.suppression.persistence.enable";
    public static final String SERVICE_LOW_SUPPRESSION_PERSISTENCE_ENABLE = "service.low.suppression.persistence.enable";

    public static final Boolean SERVICE_HIGH_SUPPRESSION_PERSISTENCE_ENABLE_FLAG = true;
    public static final Boolean SERVICE_MEDIUM_SUPPRESSION_PERSISTENCE_ENABLE_FLAG = true;
    public static final Boolean SERVICE_LOW_SUPPRESSION_PERSISTENCE_ENABLE_FLAG = true;

    public static final String SERVICE_CLOSING_WINDOW_PROPERTY_NAME = "service.closing.window";
    public static final String SERVICE_CLOSING_WINDOW = "3";
    public static final String SERVICE_MAX_DATA_BREAKS_PROPERTY_NAME = "service.max.data.breaks";
    public static final String SERVICE_MAX_DATA_BREAKS = "30";

    // request params
    public static final String KPI_TYPE = "KPI";
    public static final String ALL = "ALL";
    public static final String ACCOUNT_IDENTIFIER = "accountIdentifier";
    public static final String SERVICE_IDENTIFIER = "serviceIdentifier";
    public static final String THRESHOLD_TYPE = "thresholdType";
    public static final String IS_SYSTEM = "isSystem";
    public static final String AUTH_KEY = "authKey";
    public static final String ACCOUNT_ID = "accountId";
    public static final String SERVICE_ID = "serviceId";
}
