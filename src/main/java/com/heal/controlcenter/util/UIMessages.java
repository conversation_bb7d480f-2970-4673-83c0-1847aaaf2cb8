package com.heal.controlcenter.util;

public class UIMessages {

    private UIMessages() {
        //Dummy Constructor
    }

    /**
     * Client validations related
     */
    public static final String AUTH_KEY_INVALID = "Invalid authorization token. Reason: It should not be null or empty.";
    public static final String AUTH_KEY_EXCEPTION_MESSAGE = "Invalid authorization token";
    public static final String ACCOUNT_IDENTIFIER_INVALID = "Invalid account identifier. Reason: It should not be null or empty.";
    public static final String ACCOUNT_IDENTIFIER_EXCEPTION_MESSAGE = "Invalid account identifier";
    public static final String USERID_EXTRACTION_FAILURE = "Error occurred while fetching userId from authorization token. Details: ";
    public static final String USERID_EXTRACTION_EXCEPTION_MESSAGE = "Error occurred while fetching userId from authorization token";
    public static final String REQUEST_BODY_NULL = "Invalid request. Reason: It should not be null or empty.";
    public static final String REQUEST_BODY_NULL_EXCEPTION_MESSAGE = "Invalid request";

    public static final String SERVICE_IDENTIFIER_INVALID = "Invalid service identifier. Reason: It should not be null or empty.";
    public static final String SERVICE_IDENTIFIER_EXCEPTION_MESSAGE = "Invalid service identifier";
    public static final String SERVICE_ID_INVALID = "Invalid serviceId. Reason: It is either null or empty.";
    public static final String SERVICE_ID_INVALID_EXCEPTION_MESSAGE = "Invalid serviceId";
    public static final String SERVICE_ID_INVALID_INTEGER = "Invalid serviceId [%s]. Reason: It is not a valid integer.";


    /**
     * Server validations related
     */
    public static final String ACCOUNT_IDENTIFIER_UNAVAILABLE = "Account with identifier [%s] is unavailable";
    public static final String SERVICE_IDENTIFIER_UNAVAILABLE = "Service with identifier [%s] is unavailable";
    public static final String INVALID_USER_ACCESS_DETAILS= "access details is not available";
}
