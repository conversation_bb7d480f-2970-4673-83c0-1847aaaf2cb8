package com.heal.controlcenter.util;

import com.appnomic.appsone.common.util.Commons;
import com.appnomic.appsone.model.JWTData;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.security.Security;
import java.util.Base64;

@Component
@Slf4j
public class CommonUtils {

    @Autowired
    KeyCloakAuthService keyCloakAuthService;

    public boolean isEmpty(String s) {
        return (s == null || s.trim().length() == 0);
    }

    public static ObjectMapper getObjectMapperWithHtmlEncoder() {
        ObjectMapper objectMapper = new ObjectMapper();

        SimpleModule simpleModule = new SimpleModule("HTML-Encoder", objectMapper.version()).addDeserializer(String.class, new EscapeHTML());

        objectMapper.registerModule(simpleModule);

        return objectMapper;
    }

    public String getUserId(String authKey) throws ControlCenterException {
        JWTData jwtData = keyCloakAuthService.extractUserDetails(authKey);
        return jwtData.getSub();
    }

    public static String getDecryptedData(String encryptedData) {
        //TODO BouncyCastle encryption algorithm has to be used in further releases.
        return new String(Base64.getDecoder().decode(encryptedData));
    }

    public String decryptInBCEC(String input) throws ServerException {
        if (input != null && !input.isEmpty()) {
            try {
                Security.removeProvider(Constants.BC_PROVIDER_NAME);
                return Commons.decrypt(input);
            } catch (Exception e) {
                log.error("Exception encountered while decrypting the password. Details: {}", e.getMessage());
                throw new ServerException("Error occurred while decrypting the password.");
            }
        }
        return "";
    }

    public String encryptInBCEC(String input) throws ServerException {
        if (input != null && !input.isEmpty()) {
            try {
                Security.removeProvider(Constants.BC_PROVIDER_NAME);
                return Commons.encrypt(input);
            } catch (Exception e) {
                log.error("Exception encountered while decrypting the password. Details: {}", e.getMessage(), e);
                throw new ServerException("Error occurred while decrypting the password.");
            }
        }
        return "";
    }
}

class EscapeHTML extends JsonDeserializer<String> {

    @Override
    public String deserialize(JsonParser jp, DeserializationContext ctxt)
            throws IOException {
        String s = jp.getValueAsString();
        return StringEscapeUtils.escapeHtml4(s);
    }

}

