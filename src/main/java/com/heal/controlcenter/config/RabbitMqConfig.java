package com.heal.controlcenter.config;

import com.heal.controlcenter.service.CommandForwarderToQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.connection.RabbitConnectionFactoryBean;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;

@Slf4j
@Configuration
public class RabbitMqConfig {

    @Value("${spring.rabbitmq.outputQueueName:command-messages}")
    private String outputQueueName;

    public final boolean IS_QUEUE_DURABLE = true;
    public final boolean IS_QUEUE_EXCLUSIVE = false;
    public final boolean QUEUE_AUTO_DELETE = false;

    @Qualifier("commandMessagesQueue")
    @Bean
    public Queue createInputQueue() {
        return new Queue(outputQueueName, IS_QUEUE_DURABLE, IS_QUEUE_EXCLUSIVE, QUEUE_AUTO_DELETE, null) ;
    }

    @Bean
    public CommandForwarderToQueue sender() {
        return new CommandForwarderToQueue();
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        return new RabbitTemplate(connectionFactory);
    }

    @Bean
    public ConnectionFactory createSslConnectionFactory(RabbitProperties rabbitProperties) {
        RabbitConnectionFactoryBean factory = new RabbitConnectionFactoryBean();
        if (rabbitProperties.getHost() != null) {
            factory.setHost(rabbitProperties.getHost());
            factory.setPort(rabbitProperties.getPort());
        }
        if (rabbitProperties.getUsername() != null) {
            factory.setUsername(rabbitProperties.getUsername());
        }
        if (rabbitProperties.getPassword() != null) {
            factory.setPassword(rabbitProperties.getPassword());
        }

        RabbitProperties.Ssl ssl = rabbitProperties.getSsl();
        if (ssl.getEnabled()) {
            factory.setUseSSL(true);
            factory.setEnableHostnameVerification(false);
            factory.setSslAlgorithm(ssl.getAlgorithm());
        }

        factory.afterPropertiesSet();

        CachingConnectionFactory connectionFactory = null;
        try {
            connectionFactory = new CachingConnectionFactory(Objects.requireNonNull(factory.getObject()));
            connectionFactory.setAddresses(rabbitProperties.getAddresses());
        } catch (Exception e) {
            log.error("Exception occurred while creating ConnectionFactory. Details: ", e);
        }

        return connectionFactory;
    }
}
