package com.heal.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@JsonPropertyOrder({"data", "message", "status"})
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ResponsePojo<T> {

    private String message;
    private T data;
    private HttpStatus responseStatus;
}

