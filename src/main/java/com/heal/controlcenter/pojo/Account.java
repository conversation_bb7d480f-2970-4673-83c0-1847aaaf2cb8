package com.heal.controlcenter.pojo;

import com.heal.controlcenter.beans.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Account {
    private Integer accountId;
    private String accountName;
    private int status;
    private String privateKey;
    private String publicKey;
    private List<Tag> tags;
    private long timezoneMilli;
    private String timeZoneString;
    private Long updatedTime;
    private String updatedBy;
    private String dateFormat;
    private String timeFormat;
    private String identifier;
}