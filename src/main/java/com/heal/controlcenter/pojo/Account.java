package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Account {
    private Integer accountId;
    private String accountName;
    private String identifier;
    private int status;
    private String privateKey;
    private String publicKey;
    private List<Tags> tags;
    private long timezoneMilli;
    private String timeZoneString;
    private Long updatedTime;
    private String updatedBy;
    private String dateFormat;
    private String timeFormat;
}