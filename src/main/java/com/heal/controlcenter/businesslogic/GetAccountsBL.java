package com.heal.controlcenter.businesslogic;

import com.google.gson.reflect.TypeToken;
import com.heal.controlcenter.beans.AccessDetailsBean;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.beans.UserAccessBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.UIMessages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetAccountsBL implements BusinessLogic<Account,AccessDetailsBean, List<Account>> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountDao;

    @Override
    public UtilityBean<Account> clientValidation(Account requestBody, String... requestParams) throws ClientException {
        String authToken = requestParams[0];

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE);
        }
        return UtilityBean.<Account>builder()
                .authToken(authToken)
                .build();
    }

    @Override
    public AccessDetailsBean serverValidation(UtilityBean<Account> utilityBean) throws ServerException{
        String userId;
        try {
            userId = commonUtils.getUserId(utilityBean.getAuthToken());
        } catch (ControlCenterException e) {
            log.error("Exception encountered while fetching the userIdentifier. Details: {}", e.getMessage());
            throw new ServerException("Error while extracting user identifier");
        }

        UserAccessBean accessDetails;
        accessDetails = accountDao.fetchUserAccessDetailsUsingIdentifier(userId);

        Type userBeanType = new TypeToken<AccessDetailsBean>() {
        }.getType();

        AccessDetailsBean bean = CommonUtils.jsonToObject(accessDetails.getAccessDetails(), userBeanType);
        if (bean == null) {
            log.error(UIMessages.INVALID_USER_ACCESS_DETAILS);
            throw new ServerException(UIMessages.INVALID_USER_ACCESS_DETAILS);
        }
        return bean;
    }

    @Override
    public List<Account> process(AccessDetailsBean bean) throws DataProcessingException, ControlCenterException {
        List<com.heal.configuration.pojos.Account> accessibleAccounts = new AccountRepo().getAccounts();
        if (Objects.nonNull(accessibleAccounts) && !bean.getAccounts().contains("*")) {
            accessibleAccounts = accessibleAccounts.parallelStream().filter(acc -> bean.getAccounts().contains(acc.getIdentifier())).collect(Collectors.toList());
        }

        List<Account> accountsList = new ArrayList<>();

        for (com.heal.configuration.pojos.Account accountBean : accessibleAccounts) {
            TimezoneBean timezoneBean;
            try {
                timezoneBean = accountDao.getAccountTimezoneDetails(accountBean.getId());
            } catch (ControlCenterException e) {
                throw new DataProcessingException(e.getMessage());
            }

            if (timezoneBean == null) {
                continue;
            }
            Account account = Account.builder()
                    .accountId(accountBean.getId())
                    .accountName(accountBean.getName())
                    .identifier(accountBean.getIdentifier())
                    .privateKey(accountBean.getPrivateKey())
                    .publicKey(accountBean.getPublicKey())
                    .updatedBy(accountBean.getLastModifiedBy())
                    .updatedTime(accountBean.getUpdatedTime() != null ?
                            Timestamp.valueOf(accountBean.getUpdatedTime()).getTime() : null)
                    .timezoneMilli(timezoneBean != null ? timezoneBean.getOffset() * 60 * 1000L : 0L)
                    .timeZoneString(timezoneBean != null ? timezoneBean.getTimeZoneId() : "UTC")
                    .status(accountBean.getStatus())
                    .dateFormat("YYYY-MM-DD")
                    .timeFormat("HH:mm")
                    .build();
            accountsList.add(account);
        }
        return accountsList;
    }
}

