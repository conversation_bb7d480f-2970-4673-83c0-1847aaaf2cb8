package com.heal.controlcenter.businesslogic;

import com.google.gson.reflect.TypeToken;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.TimeZoneDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.ForensicActionsPojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.UIMessages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetAccountsBL implements BusinessLogic<Account,AccessDetailsBean, List<Account>>{

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountDao;

    @Override
    public UtilityBean<Account> clientValidation(Account requestBody, String... requestParams) throws ClientException {
        String authToken = requestParams[0];

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE);
        }

        String userId;
        try {
            userId = commonUtils.getUserId(authToken);
        } catch (ControlCenterException e) {
            log.error(UIMessages.USERID_EXTRACTION_FAILURE, e);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        if (userId == null) {
            log.error(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        return UtilityBean.<Account>builder()
                .authToken(authToken)
                .userId(userId)
                .build();
    }

    @Override
    public AccessDetailsBean serverValidation(UtilityBean<Account> utilityBean) throws ServerException{
        return null;
    }

    @Override
    public List<Account> process(AccessDetailsBean bean) throws DataProcessingException, ControlCenterException {
        try {
            List<AccountBean> accountBeans = accountDao.getAccounts();

            return accountBeans.parallelStream().map(accountBean -> {
                TimezoneBean timezoneBean = null;
                try {
                    timezoneBean = accountDao.getAccountTimezoneDetails(accountBean.getId());
                } catch (ControlCenterException e) {
                    log.warn("Timezone not found for account id [{}], setting defaults", accountBean.getId());
                }

                return Account.builder()
                        .accountId(accountBean.getId())
                        .accountName(accountBean.getName())
                        .identifier(accountBean.getIdentifier())
                        .status(accountBean.getStatus())
                        .privateKey(accountBean.getPrivateKey())
                        .publicKey(accountBean.getPublicKey())
                        .timezoneMilli(timezoneBean != null ? timezoneBean.getOffset() * 60 * 1000L : 0L)
                        .timeZoneString(timezoneBean != null ? timezoneBean.getTimeZoneId() : "UTC")
                        .updatedTime(accountBean.getUpdatedTime() != null ?
                                Timestamp.valueOf(accountBean.getUpdatedTime()).getTime() : null)
                        .updatedBy(accountBean.getLastModifiedBy())
                        .dateFormat("YYYY-MM-DD")
                        .timeFormat("HH:mm")
                        .build();
            }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error processing account list", e);
            throw new ControlCenterException("Failed to process accounts");
        }
    }
}

