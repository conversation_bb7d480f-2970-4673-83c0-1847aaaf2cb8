package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.MasterTimezoneBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.ForensicActionsPojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.UIMessages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetAccountsBL implements BusinessLogic<Object, Integer, List<Account>>{

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountDao;

    @Override
    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {
        String authToken = requestParams[0];

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE);
        }

        String userId;
        try {
            userId = commonUtils.getUserId(authToken);
        } catch (ControlCenterException e) {
            log.error(UIMessages.USERID_EXTRACTION_FAILURE, e);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        return UtilityBean.builder()
                .authToken(authToken)
                .userId(userId)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Object> utilityBean) {
        return null;
    }

    @Override
    public List<Account> process(Integer accountId) throws DataProcessingException, ControlCenterException {
        try {
            List<AccountBean> accountBeans = accountDao.getAccounts();

            return accountBeans.parallelStream().map(accountBean -> {
                MasterTimezoneBean timezoneBean = null;
                try {
                    timezoneBean = accountDao.getAccountTimezoneDetails(accountBean.getId());
                } catch (ControlCenterException e) {
                    log.warn("Timezone not found for account id [{}], setting defaults", accountBean.getId());
                }

                return Account.builder()
                        .accountId(accountBean.getId())
                        .accountName(accountBean.getName())
                        .status(accountBean.getStatus())
                        .privateKey(accountBean.getPrivateKey())
                        .publicKey(accountBean.getPublicKey())
                        .identifier(accountBean.getIdentifier())
                        .updatedBy(accountBean.getLastModifiedBy())
                        .updatedTime(accountBean.getUpdatedTime() != null ?
                                Timestamp.valueOf(accountBean.getUpdatedTime()).getTime() : null)
                        .timeZoneString(timezoneBean != null ? timezoneBean.getTimeZoneId() : "UTC")
                        .timezoneMilli(timezoneBean != null ? timezoneBean.getTimeOffset() * 60 * 1000L : 0L)
                        .build();
            }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error processing account list", e);
            throw new ControlCenterException("Failed to process accounts");
        }
    }
}

