package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.AutoDiscoveryDao;
import com.heal.controlcenter.enums.Entity;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AutoDiscoveryIgnoredEntitiesPojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.UIMessages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetAutoDiscoveryIgnoredEntitiesBL implements BusinessLogic<Object, Object, List<AutoDiscoveryIgnoredEntitiesPojo>> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountDao;
    @Autowired
    AutoDiscoveryDao autoDiscoveryDao;

    @Override
    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {
        String userId;

        if (requestParams[0].isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE);
        }

        if (requestParams[1].isEmpty()) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.ACCOUNT_IDENTIFIER_EXCEPTION_MESSAGE);
        }

        try {
            userId = commonUtils.getUserId(requestParams[0]);
        } catch (ControlCenterException e) {
            log.error(UIMessages.USERID_EXTRACTION_FAILURE, e);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        if (userId == null) {
            log.error(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        return UtilityBean.builder()
                .authToken(requestParams[0])
                .accountIdentifier(requestParams[1])
                .userId(userId)
                .build();
    }

    @Override
    public Object serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = accountDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, utilityBean.getAccountIdentifier());
            log.error(message);
            throw new ServerException(message);
        }

        return null;
    }

    @Override
    public List<AutoDiscoveryIgnoredEntitiesPojo> process(Object bean) throws DataProcessingException {
        // fetch ignored hosts
        List<AutoDiscoveryHostBean> ignoredHostsList = autoDiscoveryDao.getIgnoredHosts();
        // fetch ignored processes
        List<AutoDiscoveryProcessBean> ignoredProcessesList = autoDiscoveryDao.getIgnoredProcesses();

        Map<String, List<String>> hostIdentifierWiseNetworkInterfaceMap = autoDiscoveryDao.getNetworkInterfacesList()
                .parallelStream().collect(Collectors.groupingBy(NetworkInterfaceBean::getHostIdentifier,
                        Collectors.mapping(NetworkInterfaceBean::getInterfaceIP, Collectors.toList())));

        Map<String, List<AutoDiscoveryDiscoveredAttributesBean>> hostIdentifierWiseApplicableDiscoveredAttributes = autoDiscoveryDao.getDiscoveredAttributesList()
                .parallelStream()
                .filter(attr -> attr.getEntityType().toString().equalsIgnoreCase("Host") &&
                        (attr.getAttributeName().equalsIgnoreCase("SshPort") ||
                        attr.getAttributeName().equalsIgnoreCase("MonitorPort") ||
                        attr.getAttributeName().equalsIgnoreCase("JMXPort")))
                .collect(Collectors.groupingBy(AutoDiscoveryDiscoveredAttributesBean::getDiscoveredAttributesIdentifier,
                        Collectors.mapping(Function.identity(), Collectors.toList())));

        List<AutoDiscoveryIgnoredEntitiesPojo> ignoredHosts = ignoredHostsList.parallelStream().map(host -> {
            AutoDiscoveryIgnoredEntitiesPojo temp = new AutoDiscoveryIgnoredEntitiesPojo();
            temp.setName(host.getHostname());
            temp.setIdentifier(host.getHostIdentifier());
            temp.setEntityType(Entity.Host);
            temp.setLastDiscoveryRunTime(host.getLastDiscoveryRunTime());
            temp.setLastUpdatedTime(host.getLastUpdatedTime());
            temp.setIgnoredBy(host.getIgnoredBy());
            temp.setHostAddress(hostIdentifierWiseNetworkInterfaceMap.getOrDefault(host.getHostIdentifier(), new ArrayList<>()));

            List<AutoDiscoveryDiscoveredAttributesBean> entityAttributes = hostIdentifierWiseApplicableDiscoveredAttributes
                    .getOrDefault(host.getHostIdentifier(), new ArrayList<>());

            ArrayList<String> attributes = new ArrayList<>();
            for (AutoDiscoveryDiscoveredAttributesBean attr : entityAttributes) {
                attributes.add(attr.getAttributeName() + ":" + attr.getAttributeValue());
            }
            temp.setDiscoveredEntities(attributes);

            return temp;
        }).collect(Collectors.toList());

        List<AutoDiscoveryIgnoredEntitiesPojo> ignoredProcesses = ignoredProcessesList.parallelStream().map(process -> {
            AutoDiscoveryIgnoredEntitiesPojo temp = new AutoDiscoveryIgnoredEntitiesPojo();
            temp.setName(process.getProcessName());
            temp.setIdentifier(process.getProcessIdentifier());
            temp.setEntityType(Entity.CompInstance);
            temp.setIgnoredBy(process.getIgnoredBy());
            temp.setLastUpdatedTime(process.getLastUpdatedTime());
            temp.setLastDiscoveryRunTime(process.getLastDiscoveryRunTime());
            temp.setHostAddress(hostIdentifierWiseNetworkInterfaceMap.getOrDefault(process.getHostIdentifier(), new ArrayList<>()));

            List<AutoDiscoveryDiscoveredAttributesBean> entityAttributes = hostIdentifierWiseApplicableDiscoveredAttributes
                    .getOrDefault(process.getProcessIdentifier(), new ArrayList<>());

            ArrayList<String> attributes = new ArrayList<>();
            for (AutoDiscoveryDiscoveredAttributesBean attr : entityAttributes) {
                attributes.add(attr.getAttributeName() + ":" + attr.getAttributeValue());
            }
            temp.setDiscoveredEntities(attributes);

            return temp;
        }).collect(Collectors.toList());

        List<AutoDiscoveryIgnoredEntitiesPojo> finalIgnoredEntities = new ArrayList<>();
        finalIgnoredEntities.addAll(ignoredHosts);
        finalIgnoredEntities.addAll(ignoredProcesses);

        finalIgnoredEntities.sort(Comparator.comparing(AutoDiscoveryIgnoredEntitiesPojo::getLastDiscoveryRunTime, Comparator.reverseOrder())
                .thenComparing(AutoDiscoveryIgnoredEntitiesPojo::getName, Comparator.comparingInt(o -> Character.toLowerCase(o.charAt(0)))));

        return finalIgnoredEntities;
    }
}
