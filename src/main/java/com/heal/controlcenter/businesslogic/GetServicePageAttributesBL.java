package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.enums.AttributeSelectionType;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ServicePageAttributePojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.UIMessages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class GetServicePageAttributesBL implements BusinessLogic<Integer, Integer, List<ServicePageAttributePojo>> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountDao;
    @Autowired
    ControllerDao controllerDao;

    @Override
    public UtilityBean<Integer> clientValidation(Integer requestBody, String... requestParams) throws ClientException {
        String userId;

        if (requestParams[0].isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE);
        }

        if (requestParams[1].isEmpty()) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.ACCOUNT_IDENTIFIER_EXCEPTION_MESSAGE);
        }

        if (requestParams[2].isEmpty()) {
            log.error("Invalid serviceId. Reason: It is either null or empty.");
            throw new ClientException("Invalid serviceId");
        }

        int serviceId;
        try {
            serviceId = Integer.parseInt(requestParams[2]);
        } catch (NumberFormatException e) {
            log.error("Invalid serviceId [{}]. Reason: It is not a valid integer.", requestParams[2]);
            throw new ClientException("Invalid serviceId");
        }

        if(serviceId <= 0) {
            log.error("Invalid serviceId [{}]. Reason: It is not a valid integer.", serviceId);
            throw new ClientException("Invalid serviceId");
        }

        try {
            userId = commonUtils.getUserId(requestParams[0]);
        } catch (ControlCenterException e) {
            log.error(UIMessages.USERID_EXTRACTION_FAILURE, e);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        if (userId == null) {
            log.error(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        return UtilityBean.<Integer>builder()
                .authToken(requestParams[0])
                .accountIdentifier(requestParams[1])
                .userId(userId)
                .pojoObject(serviceId)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        AccountBean account = accountDao.getAccountByIdentifier(utilityBean.getAccountIdentifier());
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, utilityBean.getAccountIdentifier());
            log.error(message);
            throw new ServerException(message);
        }

        ControllerBean service = controllerDao.getServiceById(utilityBean.getPojoObject(), account.getId());
        if (null == service) {
            String message = String.format("ServiceId [%d] is unavailable for account [%s]", utilityBean.getPojoObject(), utilityBean.getAccountIdentifier());
            log.error(message);
            throw new ServerException(message);
        }

        return service.getId();
    }

    @Override
    public List<ServicePageAttributePojo> process(Integer serviceId) throws DataProcessingException {
        List<ServicePageAttributePojo> attributes = new ArrayList<>();

        int id = 1;
        attributes.add(new ServicePageAttributePojo(id++, null, "Name", "name", "name",
                null, null, null, new ServicePageAttributePojo.AttributeProperties(1, 128,
                null, "^[a-zA-Z0-9._-]+$", AttributeSelectionType.TextBox, 1, 0,
                0, 1, null)));

        attributes.add(new ServicePageAttributePojo(id++, null, "Identifier", "identifier", "identifier",
                null, null, null, new ServicePageAttributePojo
                .AttributeProperties(1, 128, null, "^[a-zA-Z0-9._-]+$", AttributeSelectionType.TextBox,
                0, serviceId == 0 ? 0 : 1, 0, 1, null)));

        attributes.add(new ServicePageAttributePojo(id++, null, "Application(s)", "appIdentifiers",
                "application.identifier", "name", "identifier",
                "accounts/{accountIdentifier}/applications", new ServicePageAttributePojo.AttributeProperties(0,
                0, null, "", AttributeSelectionType.Dropdown, 1, 0, 1,
                0, null)));

        attributes.add(new ServicePageAttributePojo(id++, null, "Layer", "layer", "layer",
                null, null, null, new ServicePageAttributePojo.AttributeProperties(1, 128,
                controllerDao.getLayers(Constants.SERVICES_LAYER_TYPE), "^[a-zA-Z0-9._-]+$", AttributeSelectionType.Dropdown,
                1, 0, 0, 0, null)));

        attributes.add(new ServicePageAttributePojo(id++, null, "Timezone", "timezone", "timezone",
                "timeZoneId", "timeZoneId", "/timezones", new ServicePageAttributePojo.AttributeProperties(0,
                0, null, "", AttributeSelectionType.Dropdown, 1, 0, 0,
                0, null)));

        if (serviceId == 0) {
            Map<String, String> options = new HashMap<>();
            options.put("0", "No");
            options.put("1", "Yes");
            attributes.add(new ServicePageAttributePojo(id, 0, "Mark as Entry Point", "isEntryPointService",
                    "isEntryPoint", null, null, null, new ServicePageAttributePojo.
                    AttributeProperties(0, 0, options, "", AttributeSelectionType.Switch, 0,
                    0, 0, 0, null)));
        }

        return attributes;
    }
}
