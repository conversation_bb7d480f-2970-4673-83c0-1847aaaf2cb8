package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.PostAccountBean;
import com.heal.controlcenter.beans.TagBean;
import com.heal.controlcenter.beans.ThresholdSeverityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.pojo.AccountRequest;
import com.heal.controlcenter.pojo.AccountResponse;
import com.heal.controlcenter.pojo.TagResponse;
import com.heal.controlcenter.util.KeyGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.security.KeyPair;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@RequiredArgsConstructor
public class PostAccountService {
    private final AccountsDao accountsDao;

    public int createAccount(AccountRequest req) throws ControlCenterException {
        KeyPair keyPair = KeyGenerator.generateKeys();
        String publicKey = KeyGenerator.getPublicKey(null, keyPair);
        String privateKey = KeyGenerator.getPrivateKey(null, keyPair);

        String updatedBy = "system";
        String updatedTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        int accountId = accountsDao.insertAccount(req, publicKey, privateKey, updatedBy, updatedTime);

        if (req.getThresholdSeverityBean() != null) {
            accountsDao.insertThresholdSeverity(req.getThresholdSeverityBean(), accountId);
        }

        // Fixed: use getTag() instead of getTagBean()
        if (req.getTag() != null && !req.getTag().isEmpty()) {
            for (TagBean tagBean : req.getTag()) {
                accountsDao.insertTag(tagBean, accountId);
            }
        }

        return accountId;
    }

    public PostAccountBean getAccountById(int accountId) {
        return accountsDao.getAccountById(accountId);
    }

    public static AccountResponse buildResponse(PostAccountBean postAccountBean) {
        AccountResponse resp = new AccountResponse();
        resp.setIdentifier(postAccountBean.getIdentifier());
        resp.setAccountName(postAccountBean.getAccountName());
        resp.setClosingWindow(postAccountBean.getClosingWindow());
        resp.setMaxDataBreaks(postAccountBean.getMaxDataBreaks());
        resp.setStatus(postAccountBean.getStatus());

        ThresholdSeverityBean ts = postAccountBean.getThresholdSeverityBean();
        if (ts != null) {
            resp.setThresholdSeverityBean(ts);
        }

        List<TagBean> tagBeanList = postAccountBean.getTagBean();
        if (tagBeanList != null && !tagBeanList.isEmpty()) {
            List<TagResponse> tagResponses = tagBeanList.stream().map(tag -> {
                TagResponse tagResp = new TagResponse();
                tagResp.setId(tag.getId());
                tagResp.setName(tag.getName());
                tagResp.setIdentifier(tag.getIdentifier());
                return tagResp;
            }).toList();
            resp.setTag(tagResponses);
        }

        return resp;
    }
}