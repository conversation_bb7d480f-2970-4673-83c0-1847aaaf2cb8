package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.SMTPDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.SMTPDetailsPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.DataLengthException;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

@Slf4j
@Component
public class PostEmailConfigurationsBL implements BusinessLogic<SMTPDetailsPojo, UtilityBean<SMTPDetailsPojo>, Object> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountsDao;
    @Autowired
    NotificationsDao notificationsDao;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    AECSBouncyCastleUtil aecsBouncyCastleUtil;
    @Autowired
    DateTimeUtil dateTimeUtil;

    private ViewTypesBean securityType;

    @Override
    public UtilityBean<SMTPDetailsPojo> clientValidation(SMTPDetailsPojo smtpDetails, String... requestParams) throws ClientException {
        String userId;

        if (requestParams[0].isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE);
        }

        if (requestParams[1].isEmpty()) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.ACCOUNT_IDENTIFIER_EXCEPTION_MESSAGE);
        }

        try {
            userId = commonUtils.getUserId(requestParams[0]);
        } catch (ControlCenterException e) {
            log.error(UIMessages.USERID_EXTRACTION_FAILURE, e);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        if (userId == null) {
            log.error(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        Map<String, String> error = smtpDetails.validate();
        if (error != null && !error.isEmpty()) {
            String err = error.toString();
            log.error(err);
            throw new ClientException(err);
        }

        return UtilityBean.<SMTPDetailsPojo>builder()
                .authToken(requestParams[0])
                .accountIdentifier(requestParams[1])
                .pojoObject(smtpDetails)
                .userId(userId)
                .build();
    }

    @Override
    public UtilityBean<SMTPDetailsPojo> serverValidation(UtilityBean<SMTPDetailsPojo> utilityBean) throws ServerException {
        AccountBean account = accountsDao.getAccountByIdentifier(utilityBean.getAccountIdentifier());
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, utilityBean.getAccountIdentifier());
            log.error(message);
            throw new ServerException(message);
        }

        utilityBean.setAccount(account);

        SMTPDetailsBean smtpDetailsBeanExists = notificationsDao.getSMTPDetails(account.getId());
        if (smtpDetailsBeanExists != null) {
            log.error("SMTP settings are already available for accountId [{}].", account.getId());
            throw new ServerException("SMTP settings already present.");
        }

        SMTPDetailsPojo smtpDetails = utilityBean.getPojoObject();
        String plainTxt = "";

        try {
            securityType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SMTP_PROTOCOLS, smtpDetails.getSecurity());
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        if (securityType == null) {
            String err = "Security type details is unavailable for security type: ".concat(smtpDetails.getSecurity());
            log.error(err);
            throw new ServerException(err);
        }

        if (smtpDetails.getPassword() == null || smtpDetails.getPassword().trim().isEmpty()) {
            smtpDetails.setPassword("");
        } else {
            try {
                plainTxt = aecsBouncyCastleUtil.decrypt(smtpDetails.getPassword());
                if (plainTxt.isEmpty()) {
                    String err = "Error occurred, Password is not encrypted properly.";
                    log.error(err);
                    throw new ServerException(err);
                }
            } catch (InvalidCipherTextException | DataLengthException e) {
                log.error("Exception encountered while decrypting the password. Details: ", e);
                throw new ServerException("Error while decrypting the password");
            }
        }

        smtpDetails.setPassword(commonUtils.encryptInBCEC(plainTxt));

        return utilityBean;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public Object process(UtilityBean<SMTPDetailsPojo> smtpDetailsPojo) throws DataProcessingException {
        Date time = dateTimeUtil.getCurrentTimestampInGMT();
        DateFormat dateFormat = new SimpleDateFormat(Constants.DATE_TIME);
        String createdTime = dateFormat.format(time);

        SMTPDetailsPojo smtpDetails = smtpDetailsPojo.getPojoObject();

        SMTPDetailsBean smtpDetailsBean = SMTPDetailsBean.builder()
                .accountId(smtpDetailsPojo.getAccount().getId())
                .address(smtpDetails.getAddress())
                .port(smtpDetails.getPort())
                .lastModifiedBy(smtpDetailsPojo.getUserId())
                .username(smtpDetails.getUsername())
                .password(smtpDetails.getPassword())
                .securityId(securityType.getSubTypeId())
                .createdTime(createdTime)
                .updatedTime(createdTime)
                .status(1)
                .fromRecipient(smtpDetails.getFromRecipient())
                .build();

        try {
            notificationsDao.addSMTPDetails(smtpDetailsBean);
        } catch (ControlCenterException e) {
            log.error("Error occurred while adding SMTP details to account. Details: ", e);
            throw new DataProcessingException(e.getMessage());
        }

        return null;
    }
}
