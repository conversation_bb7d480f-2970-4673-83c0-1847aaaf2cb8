package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.beans.discovery.Attribute;
import com.appnomic.appsone.common.beans.discovery.AttributeAccess;
import com.appnomic.appsone.common.beans.discovery.Component;
import com.heal.controlcenter.beans.AutoDiscoveryKnownComponentAttributesBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AutoDiscoveryDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@org.springframework.stereotype.Component
public class GetAutoDiscoveryKnownComponentsBL implements BusinessLogic<Object, Object, List<Component>> {

    @Autowired
    AutoDiscoveryDao autoDiscoveryDao;

    @Override
    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {
        return null;
    }

    @Override
    public Object serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        return null;
    }

    @Override
    public List<Component> process(Object bean) throws DataProcessingException {
        /*
          Fetches all known components along with component related info such as
          'discovery_pattern' and list of 'attributes'
        */
        List<Component> knownCompAttrList = new ArrayList<>();
        List<AutoDiscoveryKnownComponentAttributesBean> knownCompAttrBeans = autoDiscoveryDao.getADComponentAttributeDetails();
        if (knownCompAttrBeans.isEmpty()) {
            String txt = "Error while fetching known components and their attributes. List is empty.";
            log.error(txt);
            throw new DataProcessingException(txt);
        }

        Component prevComponent = null;
        Component knownCompAttr;
        Attribute attributesPojo;
        List<Attribute> attributesPojoList;
        AttributeAccess accessPojo;
        List<AttributeAccess> accessPojoList;
        int prevComponentId = 0;

        for (AutoDiscoveryKnownComponentAttributesBean compAttrBean : knownCompAttrBeans) {
            if(prevComponentId != compAttrBean.getId()) {
                knownCompAttr = new Component();
                attributesPojo = new Attribute();
                attributesPojoList = new ArrayList<>();
                accessPojo = new AttributeAccess();
                accessPojoList = new ArrayList<>();

                knownCompAttr.setComponentId(compAttrBean.getId());
                knownCompAttr.setComponentName(compAttrBean.getName());
                knownCompAttr.setComponentTypeId(compAttrBean.getMstComponentTypeId());
                knownCompAttr.setRelativePath(compAttrBean.getRelativePath());
                knownCompAttr.setDiscoveryPattern(compAttrBean.getDiscoveryPattern());

                attributesPojo.setAttributeName(compAttrBean.getAttributeName());

                if (compAttrBean.getMethod() == null && compAttrBean.getValue() == null) {
                    attributesPojo.setIsMandatory(0);
                } else {
                    attributesPojo.setIsMandatory(compAttrBean.getIsMandatory());
                }
                accessPojo.setMethod(compAttrBean.getMethod());
                accessPojo.setValue(compAttrBean.getValue());
                accessPojo.setPriority(compAttrBean.getPriority());
                accessPojoList.add(accessPojo);

                attributesPojo.setAccess(accessPojoList);
                attributesPojoList.add(attributesPojo);

                knownCompAttr.setAttributes(attributesPojoList);
                knownCompAttrList.add(knownCompAttr);

                prevComponent = knownCompAttr;
                prevComponentId = prevComponent.getComponentId();

            } else {
                attributesPojo = new Attribute();
                accessPojo = new AttributeAccess();
                accessPojoList = new ArrayList<>();

                assert prevComponent != null;
                List<Attribute> prevAttribute = prevComponent.getAttributes();

                if (prevAttribute.get(prevAttribute.size() - 1).getAttributeName().equals(compAttrBean.getAttributeName())) {
                    accessPojo.setMethod(compAttrBean.getMethod());
                    accessPojo.setValue(compAttrBean.getValue());
                    accessPojo.setPriority(compAttrBean.getPriority());

                    prevAttribute.get(prevAttribute.size() - 1).getAccess().add(accessPojo);

                } else {
                    attributesPojo.setAttributeName(compAttrBean.getAttributeName());

                    if (compAttrBean.getMethod() == null && compAttrBean.getValue() == null) {
                        attributesPojo.setIsMandatory(0);
                    } else {
                        attributesPojo.setIsMandatory(compAttrBean.getIsMandatory());
                    }

                    accessPojo.setMethod(compAttrBean.getMethod());
                    accessPojo.setValue(compAttrBean.getValue());
                    accessPojo.setPriority(compAttrBean.getPriority());
                    accessPojoList.add(accessPojo);
                    attributesPojo.setAccess(accessPojoList);

                    prevAttribute.add(attributesPojo);
                }
            }
        }
        return knownCompAttrList;
    }
}
