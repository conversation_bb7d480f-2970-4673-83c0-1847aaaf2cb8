package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentTypePojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.UIMessages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetAgentTypesBL implements BusinessLogic<Integer, UtilityBean<Integer>, List<AgentTypePojo>> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountDao;
    @Autowired
    ControllerDao controllerDao;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    CompInstanceDao compInstanceDao;
    @Autowired
    AgentDao agentDao;

    @Override
    public UtilityBean<Integer> clientValidation(Integer requestBody, String... requestParams) throws ClientException {
        String userId;

        if (requestParams[0].isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE);
        }

        if (requestParams[1].isEmpty()) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.ACCOUNT_IDENTIFIER_EXCEPTION_MESSAGE);
        }

        if (requestParams[2].isEmpty()) {
            log.error(UIMessages.SERVICE_ID_INVALID);
            throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
        }

        int serviceId;
        try {
            serviceId = Integer.parseInt(requestParams[2]);
        } catch (NumberFormatException e) {
            log.error(String.format(UIMessages.SERVICE_ID_INVALID_INTEGER, requestParams[2]));
            throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
        }

        if(serviceId <= 0) {
            log.error(String.format(UIMessages.SERVICE_ID_INVALID_INTEGER, requestParams[2]));
            throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
        }

        try {
            userId = commonUtils.getUserId(requestParams[0]);
        } catch (ControlCenterException e) {
            log.error(UIMessages.USERID_EXTRACTION_FAILURE, e);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        return UtilityBean.<Integer>builder()
                .authToken(requestParams[0])
                .accountIdentifier(requestParams[1])
                .userId(userId)
                .pojoObject(serviceId)
                .build();
    }

    @Override
    public UtilityBean<Integer> serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        AccountBean account = accountDao.getAccountByIdentifier(utilityBean.getAccountIdentifier());
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, utilityBean.getAccountIdentifier());
            log.error(message);
            throw new ServerException(message);
        }

        utilityBean.setAccount(account);

        ControllerBean service = controllerDao.getServiceById(utilityBean.getPojoObject(), account.getId());
        if (null == service) {
            String message = String.format("ServiceId [%d] is unavailable for account [%s]", utilityBean.getPojoObject(), utilityBean.getAccountIdentifier());
            log.error(message);
            throw new ServerException(message);
        }

        return utilityBean;
    }

    @Override
    public List<AgentTypePojo> process(UtilityBean<Integer> bean) throws DataProcessingException {
        int serviceId = bean.getPojoObject();
        int accountId = bean.getAccount().getId();

        Set<Integer> agentIds = compInstanceDao.getAgentIdForService(serviceId, accountId);
        agentIds.addAll(compInstanceDao.getAgentIdForServiceFromTagMapping(serviceId, accountId));

        if(agentIds.isEmpty()) {
            log.warn("There are no agents mapped to the service [{}]", serviceId);
            return Collections.emptyList();
        }

        return agentDao.getAgentTypeListApartFromJimAndForensicAgents(agentIds)
                .parallelStream()
                .map(h -> AgentTypePojo.builder()
                        .id(h.getAgentTypeId())
                        .name(masterDataDao.getMstSubTypeBySubTypeId(h.getAgentTypeId()).getSubTypeName())
                        .build()).filter(Objects::nonNull)
                .sorted(Comparator.comparing(AgentTypePojo::getId)
                        .thenComparing(o -> o.getName().toLowerCase()))
                .distinct()
                .collect(Collectors.toList());
    }
}
