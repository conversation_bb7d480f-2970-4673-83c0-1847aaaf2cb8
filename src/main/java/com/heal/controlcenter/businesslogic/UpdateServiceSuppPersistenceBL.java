package com.heal.controlcenter.businesslogic;

//import com.heal.configuration.pojos.AnomalyConfiguration;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.ServiceConfiguration;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.ServiceConfigurationDao;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class UpdateServiceSuppPersistenceBL implements BusinessLogic<Map<String, ServiceSuppPersistenceConfigPojo>, UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>>, Object>{

    @Autowired
    private CommonUtils commonUtils;

    @Autowired
    private AccountsDao accountsDao;

    @Autowired
    private ControllerDao controllerDao;

    @Autowired
    private UserValidationUtil userValidationUtil;

    @Autowired
    private DateTimeUtil dateTimeUtil;

    @Autowired
    private ServiceConfigurationDao serviceConfigurationDao;

    @Autowired
    private ServiceRepo serviceRepo;

    public UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> clientValidation(Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails, String... requestParams) throws ClientException, ControlCenterException {
        log.info("Inside client validation");
        String authKey = requestParams[0];
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE);
        }

        if(requestParams[1].trim().isEmpty()) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.ACCOUNT_IDENTIFIER_EXCEPTION_MESSAGE);
        }

        if(requestParams[2].trim().isEmpty()) {
            log.error(UIMessages.SERVICE_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.SERVICE_IDENTIFIER_EXCEPTION_MESSAGE);
        }

        String userId;
        try {
            userId = commonUtils.getUserId(authKey);
        } catch (Exception e) {
            log.error(UIMessages.USERID_EXTRACTION_FAILURE, e);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        try {
            if (serviceConfigDetails.isEmpty()) {
                log.error("List of service configuration details extracted from the request in empty.");
                throw new ClientException("Request object should contain service configuration details.");
            }

            for (ServiceSuppPersistenceConfigPojo configDetails : serviceConfigDetails.values()) {
                configDetails.validate();
            }

        } catch (ControlCenterException e) {
            log.error("Error occurred while validating request body. Reason: {}", e.getMessage());
            throw e;
        }

        HashMap<String,String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, requestParams[1]);
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, requestParams[2]);
        requestParamsMap.put(Constants.AUTH_KEY, authKey);

        return UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
                .pojoObject(serviceConfigDetails)
                .userId(userId)
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> serverValidation(UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> utilityBean) throws ServerException, ControlCenterException {
        log.info("Inside server validation");
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdentifier = utilityBean.getRequestParams().get(Constants.SERVICE_IDENTIFIER);

        AccountBean account = accountsDao.getAccountDetailsForIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }
        int accountId = account.getId();

        ControllerBean service = controllerDao.getServiceByIdentifier(serviceIdentifier, accountId);
        if (service == null) {
            String message = String.format(UIMessages.SERVICE_IDENTIFIER_UNAVAILABLE, serviceIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        UserAccessDetails userAccessDetails = userValidationUtil.getUserAccessDetails(utilityBean.getUserId(), account.getIdentifier());
        if (userAccessDetails == null) {
            log.error("User access details unavailable for user [{}]", utilityBean.getUserId());
            throw new ServerException("User access details unavailable");
        }

        List<ServiceSuppPersistenceConfigurationBean> serviceBeanList = serviceConfigurationDao
                .getServiceConfiguration(accountId, service.getId());

        if (null == serviceBeanList || serviceBeanList.isEmpty()) {
            log.error("Service configuration is unavailable for provided service id.");
            throw new ServerException("Service configuration is unavailable for the provided service id");
        }

        for (ServiceSuppPersistenceConfigPojo configDetails : utilityBean.getPojoObject().values()) {
            configDetails.checkIfServiceConfigExists(serviceBeanList);
        }

        HashMap<String,String> requestParamsMap = utilityBean.getRequestParams();
        requestParamsMap.put(Constants.ACCOUNT_ID, String.valueOf(accountId));
        requestParamsMap.put(Constants.SERVICE_ID, String.valueOf(service.getId()));

        return UtilityBean.<Map<String, ServiceSuppPersistenceConfigPojo>>builder()
                .pojoObject(utilityBean.getPojoObject())
                .userId(utilityBean.getUserId())
                .requestParams(requestParamsMap)
                .build();
    }

    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    @Override
    public Object process(UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> utilityBean) throws DataProcessingException, ControlCenterException {
        log.info("Inside process");
        String userId = utilityBean.getUserId();
        int serviceId = Integer.parseInt(utilityBean.getRequestParams().get(Constants.SERVICE_ID));
        int accountId = Integer.parseInt(utilityBean.getRequestParams().get(Constants.ACCOUNT_ID));
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdentifier = utilityBean.getRequestParams().get(Constants.SERVICE_IDENTIFIER);
        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetailsMap = utilityBean.getPojoObject();

        com.heal.configuration.pojos.Service serviceConfiguration = serviceRepo.getServiceConfigurationByIdentifier(accountIdentifier, serviceIdentifier);
        if(serviceConfiguration == null) {
            log.error("Service configuration unavailable for service [{}] and account [{}]", serviceIdentifier, accountIdentifier);
            throw new DataProcessingException("Service configuration unavailable for service " + serviceIdentifier);
        }

        List<ServiceSuppPersistenceConfigurationBean> list = new ArrayList<>();
        for (ServiceSuppPersistenceConfigPojo s : serviceConfigDetailsMap.values()) {
            list.add(ServiceSuppPersistenceConfigurationBean.builder()
                    .id(s.getServiceConfigId())
                    .serviceId(serviceId)
                    .accountId(accountId)
                    .userDetailsId(userId)
                    .updatedTime(dateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .lowPersistence(s.getLowPersistence())
                    .mediumPersistence(s.getMediumPersistence())
                    .highPersistence(s.getHighPersistence())
                    .lowSuppression(s.getLowSuppression())
                    .mediumSuppression(s.getMediumSuppression())
                    .highSuppression(s.getHighSuppression())
                    .highEnable(s.isHighEnable())
                    .lowEnable(s.isLowEnable())
                    .mediumEnable(s.isMediumEnable())
                    .closingWindow(s.getClosingWindow())
                    .maxDataBreaks(s.getMaxDataBreaks())
                    .build());
        }

        ServiceSuppPersistenceConfigurationBean gte= new ServiceSuppPersistenceConfigurationBean();
        ServiceSuppPersistenceConfigPojo serviceConfigDetailsGte = serviceConfigDetailsMap.get(Constants.OPERATOR_GREATER_THAN);

        fillConfigBeanFromPojo(gte, serviceConfigDetailsGte);

        ServiceSuppPersistenceConfigurationBean lt = new  ServiceSuppPersistenceConfigurationBean();
        ServiceSuppPersistenceConfigPojo serviceConfigDetailsLt = serviceConfigDetailsMap.get(Constants.OPERATOR_LESS_THAN);

        fillConfigBeanFromPojo(lt, serviceConfigDetailsLt);

        try {
            serviceConfigurationDao.updateServiceSuppPersistenceConfig(list);

            serviceConfiguration.getServiceConfigurations().parallelStream().filter(f -> f.getAnomalyConfiguration() != null && f.getAnomalyConfiguration().getEndCollectionInterval() <= 59).findAny().ifPresent(newLt -> updateNewAnomalyConfigurations(lt, newLt));
            serviceConfiguration.getServiceConfigurations().parallelStream().filter(f -> f.getAnomalyConfiguration() != null && f.getAnomalyConfiguration().getEndCollectionInterval() >= 60).findAny().ifPresent(newGte -> updateNewAnomalyConfigurations(gte, newGte));
            serviceRepo.updateServiceConfigurationByServiceIdentifier(accountIdentifier, serviceIdentifier, serviceConfiguration);

        } catch (ControlCenterException e) {
            log.error("Persistence & suppression update failed for service id [{}]", serviceId);
            throw new DataProcessingException("Persistence & suppression update failed for service id " + serviceId);
        }
        return null;
    }

    private void fillConfigBeanFromPojo(ServiceSuppPersistenceConfigurationBean gte, ServiceSuppPersistenceConfigPojo serviceConfigDetailsGte) {
        gte.setLowPersistence(serviceConfigDetailsGte.getLowPersistence());
        gte.setLowSuppression(serviceConfigDetailsGte.getLowSuppression());
        gte.setHighPersistence(serviceConfigDetailsGte.getHighPersistence());
        gte.setHighSuppression(serviceConfigDetailsGte.getHighSuppression());
        gte.setMediumPersistence(serviceConfigDetailsGte.getMediumPersistence());
        gte.setMediumSuppression(serviceConfigDetailsGte.getMediumSuppression());
        gte.setLowEnable(serviceConfigDetailsGte.isLowEnable());
        gte.setHighEnable(serviceConfigDetailsGte.isHighEnable());
        gte.setMediumEnable(serviceConfigDetailsGte.isMediumEnable());
        gte.setClosingWindow(serviceConfigDetailsGte.getClosingWindow());
        gte.setMaxDataBreaks(serviceConfigDetailsGte.getMaxDataBreaks());
    }

}
