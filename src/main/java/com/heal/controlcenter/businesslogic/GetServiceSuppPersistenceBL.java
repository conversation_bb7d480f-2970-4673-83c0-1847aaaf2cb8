package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.util.ConfProperties;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.ServiceConfigurationDao;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AccountServiceKey;
import com.heal.controlcenter.pojo.ServiceSuppPersistenceConfigPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class GetServiceSuppPersistenceBL implements BusinessLogic<String, AccountServiceKey, Map<String, ServiceSuppPersistenceConfigPojo>> {

    @Autowired
    private CommonUtils commonUtils;

    @Autowired
    private AccountsDao accountsDao;

    @Autowired
    private ControllerDao controllerDao;

    @Autowired
    private UserValidationUtil userValidationUtil;

    @Autowired
    private DateTimeUtil dateTimeUtil;

    @Autowired
    private ServiceConfigurationDao serviceConfigurationDao;

    @Autowired
    private ServiceRepo serviceRepo;
    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE);
        }

        if(requestParams[1].trim().isEmpty()) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.ACCOUNT_IDENTIFIER_EXCEPTION_MESSAGE);
        }

        if(requestParams[2].trim().isEmpty()) {
            log.error(UIMessages.SERVICE_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.SERVICE_IDENTIFIER_EXCEPTION_MESSAGE);
        }

        String user;
        try {
            user = commonUtils.getUserId(authKey);
        } catch (Exception e) {
            log.error(UIMessages.USERID_EXTRACTION_FAILURE, e);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        HashMap<String,String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, requestParams[1]);
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, requestParams[2]);
        requestParamsMap.put(Constants.AUTH_KEY, authKey);

        return UtilityBean.<String>builder()
                .userId(user)
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public AccountServiceKey serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdentifier = utilityBean.getRequestParams().get(Constants.SERVICE_IDENTIFIER);
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        AccountBean account = accountsDao.getAccountDetailsForIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }
        int accountId = account.getId();

        ControllerBean service = controllerDao.getServiceByIdentifier(serviceIdentifier, accountId);
        if (service == null) {
            String message = String.format(UIMessages.SERVICE_IDENTIFIER_UNAVAILABLE, serviceIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        UserAccessDetails userAccessDetails = userValidationUtil.getUserAccessDetails(utilityBean.getUserId(), account.getIdentifier());
        if (userAccessDetails == null) {
            log.error("User access details unavailable for user [{}]", utilityBean.getUserId());
            throw new ServerException("User access details unavailable");
        }

        return new AccountServiceKey(account, authKey, service.getId());
    }

    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public Map<String, ServiceSuppPersistenceConfigPojo> process(AccountServiceKey accountServiceKey) throws ControlCenterException {
        int SERVICE_START_HOUR = ConfProperties.getInt(Constants.SERVICE_START_WITHIN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_START_TIME_WITHIN_AN_HOUR);
        int accountId = accountServiceKey.getAccount().getId();
        int serviceId = accountServiceKey.getServiceId();
        String userId = commonUtils.getUserId(accountServiceKey.getAuthorizationKey());

        List<ServiceSuppPersistenceConfigurationBean> serviceBeanList = serviceConfigurationDao.getServiceConfiguration(accountId, serviceId);

        if (null == serviceBeanList || serviceBeanList.isEmpty()) {
            log.info("Service configuration is unavailable for provided service id. Hence default configuration will be created.");
            addServiceConfiguration(serviceId, accountId, userId);
            serviceBeanList = serviceConfigurationDao.getServiceConfiguration(accountId, serviceId);
        }

        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();

        for (ServiceSuppPersistenceConfigurationBean s : serviceBeanList) {
            String collectionInterval = (s.getStartCollectionInterval() == SERVICE_START_HOUR) ?
                    Constants.OPERATOR_LESS_THAN : Constants.OPERATOR_GREATER_THAN;
            serviceConfigDetails.put(collectionInterval, ServiceSuppPersistenceConfigPojo.builder()
                    .serviceConfigId(s.getId())
                    .lowPersistence(s.getLowPersistence())
                    .lowSuppression(s.getLowSuppression())
                    .highPersistence(s.getHighPersistence())
                    .highSuppression(s.getHighSuppression())
                    .mediumPersistence(s.getMediumPersistence())
                    .mediumSuppression(s.getMediumSuppression())
                    .highEnable(s.isHighEnable())
                    .mediumEnable(s.isMediumEnable())
                    .lowEnable(s.isLowEnable())
                    .closingWindow(s.getClosingWindow())
                    .maxDataBreaks(s.getMaxDataBreaks())
                    .build());
        }

        return serviceConfigDetails;
    }

    public int[] addServiceConfiguration(int serviceId, int accountId, String userId) {
        List<ServiceSuppPersistenceConfigurationBean> list = new ArrayList<>();
        int SERVICE_START_HOUR = ConfProperties.getInt(Constants.SERVICE_START_WITHIN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_START_TIME_WITHIN_AN_HOUR);
        int SERVICE_END_HOUR = ConfProperties.getInt(Constants.SERVICE_END_WITHIN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_END_TIME_WITHIN_AN_HOUR);
        int SERVICE_START = ConfProperties.getInt(Constants.SERVICE_START_MORE_THAN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_START_TIME_MORE_THAN_AN_HOUR);
        int SERVICE_END = ConfProperties.getInt(Constants.SERVICE_END_MORE_THAN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_END_TIME_MORE_THAN_AN_HOUR);


        int LOW_PERSISTENCE_HOUR = ConfProperties.getInt(Constants.SERVICE_LOW_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_LOW_PERSISTENCE_WITHIN_AN_HOUR);
        int LOW_SUPPRESSION_HOUR = ConfProperties.getInt(Constants.SERVICE_LOW_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_LOW_SUPPRESSION_WITHIN_AN_HOUR);
        int LOW_PERSISTENCE = ConfProperties.getInt(Constants.SERVICE_LOW_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_LOW_PERSISTENCE_MORE_THAN_AN_HOUR);
        int LOW_SUPPRESSION = ConfProperties.getInt(Constants.SERVICE_LOW_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_LOW_SUPPRESSION_MORE_THAN_AN_HOUR);

        int MEDIUM_PERSISTENCE_HOUR = ConfProperties.getInt(Constants.SERVICE_MEDIUM_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_MEDIUM_PERSISTENCE_WITHIN_AN_HOUR);
        int MEDIUM_SUPPRESSION_HOUR = ConfProperties.getInt(Constants.SERVICE_MEDIUM_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_MEDIUM_SUPPRESSION_WITHIN_AN_HOUR);
        int MEDIUM_PERSISTENCE = ConfProperties.getInt(Constants.SERVICE_MEDIUM_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_MEDIUM_PERSISTENCE_MORE_THAN_AN_HOUR);
        int MEDIUM_SUPPRESSION = ConfProperties.getInt(Constants.SERVICE_MEDIUM_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_MEDIUM_SUPPRESSION_MORE_THAN_AN_HOUR);

        int HIGH_PERSISTENCE_HOUR = ConfProperties.getInt(Constants.SERVICE_HIGH_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_HIGH_PERSISTENCE_WITHIN_AN_HOUR);
        int HIGH_SUPPRESSION_HOUR = ConfProperties.getInt(Constants.SERVICE_HIGH_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_HIGH_SUPPRESSION_WITHIN_AN_HOUR);
        int HIGH_PERSISTENCE = ConfProperties.getInt(Constants.SERVICE_HIGH_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_HIGH_PERSISTENCE_MORE_THAN_AN_HOUR);
        int HIGH_SUPPRESSION = ConfProperties.getInt(Constants.SERVICE_HIGH_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME,
                Constants.SERVICE_HIGH_SUPPRESSION_MORE_THAN_AN_HOUR);

        boolean HIGH_ENABLE = ConfProperties.getBoolean(Constants.SERVICE_HIGH_SUPPRESSION_PERSISTENCE_ENABLE,
                Constants.SERVICE_HIGH_SUPPRESSION_PERSISTENCE_ENABLE_FLAG);

        boolean MEDIUM_ENABLE = ConfProperties.getBoolean(Constants.SERVICE_MEDIUM_SUPPRESSION_PERSISTENCE_ENABLE,
                Constants.SERVICE_MEDIUM_SUPPRESSION_PERSISTENCE_ENABLE_FLAG);

        boolean LOW_ENABLE = ConfProperties.getBoolean(Constants.SERVICE_LOW_SUPPRESSION_PERSISTENCE_ENABLE,
                Constants.SERVICE_LOW_SUPPRESSION_PERSISTENCE_ENABLE_FLAG);

        list.add(ServiceSuppPersistenceConfigurationBean.builder()
                .serviceId(serviceId)
                .accountId(accountId)
                .userDetailsId(userId)
                .createdTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()))
                .updatedTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()))
                .startCollectionInterval(SERVICE_START_HOUR)
                .endCollectionInterval(SERVICE_END_HOUR)
                .lowPersistence(LOW_PERSISTENCE_HOUR)
                .lowSuppression(LOW_SUPPRESSION_HOUR)
                .mediumPersistence(MEDIUM_PERSISTENCE_HOUR)
                .mediumSuppression(MEDIUM_SUPPRESSION_HOUR)
                .highPersistence(HIGH_PERSISTENCE_HOUR)
                .highSuppression(HIGH_SUPPRESSION_HOUR)
                .highEnable(HIGH_ENABLE)
                .mediumEnable(MEDIUM_ENABLE)
                .lowEnable(LOW_ENABLE)
                .closingWindow(ConfProperties.getInt(Constants.SERVICE_CLOSING_WINDOW_PROPERTY_NAME,
                        Constants.SERVICE_CLOSING_WINDOW))
                .maxDataBreaks(ConfProperties.getInt(Constants.SERVICE_MAX_DATA_BREAKS_PROPERTY_NAME,
                        Constants.SERVICE_MAX_DATA_BREAKS))
                .build());

        list.add(ServiceSuppPersistenceConfigurationBean.builder()
                .serviceId(serviceId)
                .accountId(accountId)
                .userDetailsId(userId)
                .createdTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()))
                .updatedTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()))
                .startCollectionInterval(SERVICE_START)
                .endCollectionInterval(SERVICE_END)
                .lowPersistence(LOW_PERSISTENCE)
                .lowSuppression(LOW_SUPPRESSION)
                .mediumPersistence(MEDIUM_PERSISTENCE)
                .mediumSuppression(MEDIUM_SUPPRESSION)
                .highPersistence(HIGH_PERSISTENCE)
                .highSuppression(HIGH_SUPPRESSION)
                .highEnable(HIGH_ENABLE)
                .mediumEnable(MEDIUM_ENABLE)
                .lowEnable(LOW_ENABLE)
                .closingWindow(ConfProperties.getInt(Constants.SERVICE_CLOSING_WINDOW_PROPERTY_NAME,
                                Constants.SERVICE_CLOSING_WINDOW))
                .maxDataBreaks(ConfProperties.getInt(Constants.SERVICE_MAX_DATA_BREAKS_PROPERTY_NAME,
                        Constants.SERVICE_MAX_DATA_BREAKS))
                .build());

        return serviceConfigurationDao.addServiceConfiguration(list);
    }
}
