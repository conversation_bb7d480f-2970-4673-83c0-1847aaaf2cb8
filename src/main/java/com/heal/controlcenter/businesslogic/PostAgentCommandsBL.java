package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentCommandArgumentsPojo;
import com.heal.controlcenter.pojo.AgentCommandsPojo;
import com.heal.controlcenter.pojo.AgentSnapshotDetailsPojo;
import com.heal.controlcenter.pojo.TagMappingDetails;
import com.heal.controlcenter.service.CommandForwarderToQueue;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.heal.controlcenter.util.Constants.*;

@Slf4j
@Component
public class PostAgentCommandsBL implements BusinessLogic<AgentCommandsPojo, UtilityBean<AgentCommandsPojo>, Object> {

    @Autowired
    CommonUtils commonUtils;
    @Autowired
    AccountsDao accountsDao;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    AgentDao agentDao;
    @Autowired
    TagsDao tagsDao;
    @Autowired
    ControllerDao controllerDao;
    @Autowired
    GetAgentCommandsBL getAgentCommandsBL;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    CommandDataDao commandDataDao;
    @Autowired
    CommandForwarderToQueue commandForwarderToQueue;

    private static final String SNAPSHOT_COUNT = "snapshot-count";
    private static final String SNAPSHOT_DURATION = "snapshot-duration";
    private static final String IS_EXCEPTION_ENABLED = "exceptions";
    private static final String SILENT_WINDOW = "silent-window";

    private ViewTypesBean jimAgentType;
    private int serviceId;
    private String agentType;

    @Override
    public UtilityBean<AgentCommandsPojo> clientValidation(AgentCommandsPojo agentCommandsPojo, String... requestParams) throws ClientException {
        String userId;

        if (requestParams[0].isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE);
        }

        if (requestParams[1].isEmpty()) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.ACCOUNT_IDENTIFIER_EXCEPTION_MESSAGE);
        }

        try {
            userId = commonUtils.getUserId(requestParams[0]);
        } catch (ControlCenterException e) {
            log.error(UIMessages.USERID_EXTRACTION_FAILURE, e);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        if (userId == null) {
            log.error(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        if (requestParams[2].isEmpty()) {
            log.error(UIMessages.SERVICE_ID_INVALID);
            throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
        }

        try {
            serviceId = Integer.parseInt(requestParams[2]);
        } catch (NumberFormatException e) {
            log.error(String.format(UIMessages.SERVICE_ID_INVALID_INTEGER, requestParams[2]));
            throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
        }

        if (serviceId <= 0) {
            log.error(String.format(UIMessages.SERVICE_ID_INVALID_INTEGER, requestParams[2]));
            throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
        }

        agentCommandsPojo.validate();
        if (!agentCommandsPojo.getError().isEmpty()) {
            String err = agentCommandsPojo.getError().toString();
            log.error(err);
            throw new ClientException(err);
        }

        agentType = requestParams[3];
        if (agentType.isEmpty()) {
            agentType = JIM_AGENT_SUB_TYPE;
            log.info("Agent type unavailable in the request parameter. Choosing JIM Agent by default.");
        }

        return UtilityBean.<AgentCommandsPojo>builder()
                .authToken(requestParams[0])
                .accountIdentifier(requestParams[1])
                .userId(userId)
                .pojoObject(agentCommandsPojo)
                .build();
    }

    @Override
    public UtilityBean<AgentCommandsPojo> serverValidation(UtilityBean<AgentCommandsPojo> utilityBean) throws ServerException {
        AccountBean account = accountsDao.getAccountByIdentifier(utilityBean.getAccountIdentifier());
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, utilityBean.getAccountIdentifier());
            log.error(message);
            throw new ServerException(message);
        }

        utilityBean.setAccount(account);

        ControllerBean service = controllerDao.getServiceById(serviceId, account.getId());
        if (null == service) {
            String message = String.format("ServiceId [%d] is unavailable for account [%s]", serviceId, utilityBean.getAccountIdentifier());
            log.error(message);
            throw new ServerException(message);
        }

        return utilityBean;
    }

    @Override
    public Object process(UtilityBean<AgentCommandsPojo> bean) throws DataProcessingException {
        int accountId = bean.getAccount().getId();
        AgentCommandsPojo agentCommandsPojo = bean.getPojoObject();

        try {
            jimAgentType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.AGENT_TYPE, agentType);
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

        AgentModeConfigBean configBean = agentDao.getAgentModeConfig(serviceId, accountId, jimAgentType.getSubTypeId());

        CommandDetailsBean commandSelected = getAgentCommandsBL.getSelectedCommand(agentType, agentCommandsPojo.getCommandMode());
        if (commandSelected == null) {
            log.error("Default/Selected command not found to process the request. Command mode: {}, Agent type: {}", agentCommandsPojo.getCommandMode(), agentType);
            throw new DataProcessingException("Default/Selected command not found to process the request.");
        }

        saveCommandToDB(agentCommandsPojo, bean.getUserId(), accountId, serviceId, configBean, jimAgentType.getSubTypeId(), commandSelected);

        List<String> jimAgentIdList = getJimAgents(serviceId, accountId, jimAgentType.getSubTypeId());

        if (jimAgentIdList.isEmpty()) {
            log.warn("No jim agent is setup for this service hence not sending the data to MQ.");
        }

        List<CommandArgumentBean> serviceCmdArgs = agentDao.getServiceCommandArguments(serviceId, jimAgentType.getSubTypeId(), commandSelected.getId());
        for (int i = 0; i < serviceCmdArgs.size(); i++) {
            if (serviceCmdArgs.get(i).getArgumentKey().equalsIgnoreCase("switch-mode")) {
                serviceCmdArgs.remove(i);
                break;
            }
        }

        sendCommandDetails(commandSelected, serviceCmdArgs, jimAgentType.getSubTypeId(), jimAgentIdList);

        return null;
    }

    public List<String> getJimAgents(int serviceId, int accountId, int jimAgentTypeId) {
        int tagId = tagsDao.getTagDetailsByAccountId(accountId).stream()
                .filter(tagDetailsBean -> tagDetailsBean.getName().equalsIgnoreCase(Constants.CONTROLLER_TAG))
                .findFirst()
                .map(TagDetailsBean::getId)
                .orElse(0);

        Set<Integer> agentIds = tagsDao.getTagMappingDetails(accountId).stream()
                .filter(tag -> tag.getTagId() == tagId &&
                        tag.getObjectRefTable().equalsIgnoreCase(Constants.AGENT_TABLE) &&
                        tag.getTagKey().equalsIgnoreCase(serviceId + ""))
                .map(TagMappingDetails::getObjectId)
                .collect(Collectors.toSet());

        List<AgentBean> agentBeans = agentDao.getAgentList().stream()
                .filter(agentBean -> agentIds.contains(agentBean.getId())
                        && agentBean.getAgentTypeId() == jimAgentTypeId)
                .collect(Collectors.toList());

        return agentBeans.parallelStream().map(AgentBean::getUniqueToken).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public void saveCommandToDB(AgentCommandsPojo agentCommand, String userId, int accountId, int serviceId,
                                 AgentModeConfigBean configBean, int agentTypeID, CommandDetailsBean commandDetailsBean)
            throws DataProcessingException {

        try {
            if (agentCommand.getCommandMode().equalsIgnoreCase("Auto")) {
                AgentSnapshotDetailsPojo agentSnapshotLevelDetails = agentDao.
                        getAgentSnapshotDetails((agentCommand.getArguments().getAutoSnapshotCollectionLevel()));
                agentCommand.getArguments().setAutoSnapshotCount(agentSnapshotLevelDetails.getSnapshotCount());
                agentCommand.getArguments().setAutoSnapshotDuration(agentSnapshotLevelDetails.getSnapshotDuration());
                agentCommand.getArguments().setAutoSnapshotSilentWindow(agentSnapshotLevelDetails.getSilentWindow());
            }
            if (configBean != null) {
                configBean.setCommandId(commandDetailsBean.getId());
                configBean.setUpdatedTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()));
                agentDao.updateAgentModeConfig(configBean);
            } else {
                AgentModeConfigBean configBean1 = new AgentModeConfigBean();
                configBean1.setServiceId(serviceId);
                configBean1.setAgentTypeId(agentTypeID);
                configBean1.setLastModifiedBy(userId);
                configBean1.setAccountId(accountId);
                configBean1.setCommandId(commandDetailsBean.getId());
                configBean1.setCreatedTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()));
                configBean1.setUpdatedTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()));
                agentDao.addAgentModeConfig(configBean1);
            }

            updateCommandArguments(agentCommand, jimAgentType, serviceId, userId);

        } catch (Exception e) {
            throw new DataProcessingException(e.getMessage());
        }
    }

    private void updateCommandArguments(AgentCommandsPojo agentCommand, ViewTypesBean jimAgentType, int serviceId, String userId)
            throws DataProcessingException {
        List<CommandDetailsBean> commandDetails = commandDataDao.getCommandDetailsByAgentType(jimAgentType.getSubTypeId());

        for (CommandDetailsBean cmdArgBean : commandDetails) {
            Map<String, String> argumentMap = getCommandArguments(agentCommand, cmdArgBean.getName());
            List<CommandArgumentBean> serviceCmdArgs = agentDao.getServiceCommandArguments(serviceId, jimAgentType.getSubTypeId(), cmdArgBean.getId());

            for (int i = 0; i < serviceCmdArgs.size(); i++) {
                if (serviceCmdArgs.get(i).getArgumentKey().equalsIgnoreCase("switch-mode")) {
                    serviceCmdArgs.remove(i);
                    break;
                }
            }

            List<ServiceCommandArgumentBean> updateCmdArgs = new ArrayList<>();
            List<ServiceCommandArgumentBean> insertCmdArgs = new ArrayList<>();

            for (CommandArgumentBean svcCmdArg : serviceCmdArgs) {
                if (null != svcCmdArg.getValue()) {
                    updateCmdArgs.add(ServiceCommandArgumentBean
                            .builder()
                            .id(svcCmdArg.getId())
                            .serviceId(serviceId)
                            .agentTypeId(jimAgentType.getSubTypeId())
                            .argumentValue(argumentMap.get(svcCmdArg.getArgumentKey()))
                            .lastModifiedBy(userId)
                            .updatedTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()))
                            .build());
                } else {
                    insertCmdArgs.add(ServiceCommandArgumentBean
                            .builder()
                            .commandArgumentId(svcCmdArg.getId())
                            .serviceId(serviceId)
                            .agentTypeId(jimAgentType.getSubTypeId())
                            .commandId(cmdArgBean.getId())
                            .argumentValue(argumentMap.get(svcCmdArg.getArgumentKey()))
                            .lastModifiedBy(userId)
                            .updatedTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()))
                            .createdTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()))
                            .build());
                }
            }

            try {
                if (!updateCmdArgs.isEmpty()) {
                    agentDao.updateServiceCommandArguments(updateCmdArgs);
                }
                if (!insertCmdArgs.isEmpty()) {
                    agentDao.addServiceCommandArguments(insertCmdArgs);
                }
            } catch (Exception e) {
                throw new DataProcessingException(e.getMessage());
            }
        }
    }

    private Map<String, String> getCommandArguments(AgentCommandsPojo agentCommand, String commandName) {
        Map<String, String> map = new HashMap<>();

        AgentCommandArgumentsPojo commandArguments = agentCommand.getArguments();
        if (AGENT_MODE_AUTO.equalsIgnoreCase(commandName)) {
            map.put("jvm_cpu", String.valueOf(commandArguments.getJvmCpuUtil()));
            map.put("jvm_mem", String.valueOf(commandArguments.getJvmMemUtil()));
            map.put("collection-mode", String.valueOf(commandArguments.getAutoSnapshotCollectionLevel()));
            map.put(SNAPSHOT_COUNT, String.valueOf(commandArguments.getAutoSnapshotCount()));
            map.put(SNAPSHOT_DURATION, String.valueOf(commandArguments.getAutoSnapshotDuration()));
            map.put(IS_EXCEPTION_ENABLED, String.valueOf(commandArguments.isAutoSnapshotForException()));
            map.put(SILENT_WINDOW, String.valueOf(commandArguments.getAutoSnapshotSilentWindow()));
        } else if (AGENT_MODE_VERBOSE.equalsIgnoreCase(commandName)) {
            map.put(SNAPSHOT_COUNT, String.valueOf(commandArguments.getVerboseSnapshotCount()));
            map.put(SNAPSHOT_DURATION, String.valueOf(commandArguments.getVerboseSnapshotDuration()));
            map.put(SILENT_WINDOW, String.valueOf(commandArguments.getVerboseSnapshotSilentWindow()));
        }
        return map;
    }

    private void sendCommandDetails(CommandDetailsBean commandDetailsBean, List<CommandArgumentBean> serviceCmdArgs,
                                    int agentTypeId, List<String> jimAgentIdList) {
        Map<String, String> argumentsMap = new HashMap<>();
        Map<String, String> envArgs = new HashMap<>();

        Map<String, String> commandArguments = serviceCmdArgs.stream().collect(Collectors.toMap(CommandArgumentBean::getArgumentKey,
                CommandArgumentBean::getValue));


        argumentsMap.put("AgentMode", commandDetailsBean.getName());
        argumentsMap.put("Command", commandDetailsBean.getName());
        if (AGENT_MODE_VERBOSE.equalsIgnoreCase(commandDetailsBean.getName())) {
            argumentsMap.put("SnapshotCollectionDuration", commandArguments.get("snapshot-duration"));
            argumentsMap.put("SnapshotsPerMin", commandArguments.get(SNAPSHOT_COUNT));
            argumentsMap.put("SilentWindow", commandArguments.getOrDefault(SILENT_WINDOW, Constants.AGENT_SILENT_WINDOW));
        } else if (AGENT_MODE_AUTO.equalsIgnoreCase(commandDetailsBean.getName())) {
            argumentsMap.put("SnapshotCollectionDuration", commandArguments.get("snapshot-duration"));
            argumentsMap.put("SnapshotsPerMin", commandArguments.get(SNAPSHOT_COUNT));
            argumentsMap.put("SilentWindow", commandArguments.getOrDefault(SILENT_WINDOW, Constants.AGENT_SILENT_WINDOW));

            envArgs.put("JVMCPUUtilizationThreshold", commandArguments.get("jvm_cpu"));
            envArgs.put("JVMMemUtilizationThreshold", commandArguments.get("jvm_mem"));
            envArgs.put("SnapshotsCollectionForFailures", commandArguments.get(IS_EXCEPTION_ENABLED));
        }

        String agentType = masterDataDao.getMstSubTypeBySubTypeId(agentTypeId).getSubTypeName();
        pushCommandRequestToQueue(commandDetailsBean, argumentsMap, envArgs, jimAgentIdList, agentType, "dummy");
    }

    public void pushCommandRequestToQueue(CommandDetailsBean commandDetailsBean, Map<String, String> commandArgsInMap,
                                          Map<String, String> commandEnvInMap, List<String> supervisorIdentifiers,
                                          String agentType, String agentIdentifier) {
        String commandType = masterDataDao.getMstSubTypeByTypeId(commandDetailsBean.getCommandTypeId()).get(0).getTypeName();
        String commandOutputType = masterDataDao.getMstSubTypeBySubTypeId(commandDetailsBean.getOutputTypeId()).getSubTypeName();

        CommandRequestProtos.Command command =
                CommandRequestProtos.Command.newBuilder()
                        .setCommandJobId(String.valueOf(UUID.randomUUID()))
                        .setCommand(commandDetailsBean.getName())
                        .setCommandType(commandType)
                        .setCommandOutputType(commandOutputType)
                        .setCommandExecType("Execute")
                        .setRetryNumber(3)
                        .setCommandTimeout(commandDetailsBean.getTimeOutInSecs())
                        .setSupervisorCtrlTTL(300)
                        .putAllArguments(commandArgsInMap)
                        .putAllEnvArgs(commandEnvInMap)
                        .build();

        CommandRequestProtos.CommandRequest commandDetails =
                CommandRequestProtos.CommandRequest.newBuilder().addAllSupervisorIdentifiers(supervisorIdentifiers)
                        .setAgentType(agentType)
                        .setAgentIdentifier(agentIdentifier)
                        .setTriggerSource("ControlCenter")
                        .setUserDetailsID(commandDetailsBean.getLastModifiedBy())
                        .setTriggerTime(new Date().getTime())
                        .setViolationTime(new Date().getTime())
                        .addCommands(command)
                        .build();

        commandForwarderToQueue.sendAgentCommandMessage(commandDetails);
    }
}
