package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.AutoDiscoveryDao;
import com.heal.controlcenter.dao.mysql.ConnectionDetailsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.GetConnectionPojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.UIMessages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetConnectionsBL implements BusinessLogic<Object, Integer, List<GetConnectionPojo>> {

    @Autowired
    AccountsDao accountDao;
    @Autowired
    CommonUtils commonUtils;
    @Autowired
    ControllerDao controllerDao;
    @Autowired
    ConnectionDetailsDao connectionDetailsDao;
    @Autowired
    AutoDiscoveryDao autoDiscoveryDao;
    @Autowired
    DateTimeUtil dateTimeUtil;

    @Override
    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {
        String userId;

        if (requestParams[0].isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE);
        }

        if (requestParams[1].isEmpty()) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ClientException(UIMessages.ACCOUNT_IDENTIFIER_EXCEPTION_MESSAGE);
        }

        try {
            userId = commonUtils.getUserId(requestParams[0]);
        } catch (ControlCenterException e) {
            log.error(UIMessages.USERID_EXTRACTION_FAILURE, e);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        if (userId == null) {
            log.error(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
            throw new ClientException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE);
        }

        return UtilityBean.builder()
                .authToken(requestParams[0])
                .accountIdentifier(requestParams[1])
                .userId(userId)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = accountDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, utilityBean.getAccountIdentifier());
            log.error(message);
            throw new ServerException(message);
        }

        List<ControllerBean> controllerBeanList;
        try {
            controllerBeanList = controllerDao.getServicesList(account.getId());
        } catch (ControlCenterException e) {
            return 0;
        }

        if (controllerBeanList.isEmpty()) {
            return 0;
        }

        return account.getId();
    }

    @Override
    public List<GetConnectionPojo> process(Integer accountId) throws DataProcessingException {
        if (accountId == 0) {
            return Collections.emptyList();
        }

        List<ConnectionDetailsBean> healConnectionDetailsBean = connectionDetailsDao.getConnectionsByAccountId(accountId);
        List<AutoDiscoveryDiscoveredConnectionsBean> discoveredConnectionsBeanList = autoDiscoveryDao.getDiscoveredConnectionsList();

        // From Heal UI
        List<GetConnectionPojo> connections = healConnectionDetailsBean.parallelStream()
                .map(c -> GetConnectionPojo.builder()
                        .sourceServiceId(c.getSourceId())
                        .sourceServiceName(c.getSourceName())
                        .sourceServiceIdentifier(c.getSourceIdentifier())
                        .destinationServiceId(c.getDestinationId())
                        .destinationServiceName(c.getDestinationName())
                        .destinationServiceIdentifier(c.getDestinationIdentifier())
                        .process(c.getIsDiscovery() == 0 ? "Manual" : "Auto")
                        .status(DiscoveryStatus.ADDED_TO_SYSTEM)
                        .lastDiscoveryRunTime(dateTimeUtil.getGMTToEpochTime(String.valueOf(c.getUpdatedTime())))
                        .build())
                .collect(Collectors.toList());

        // From Auto Discovery agent
        List<GetConnectionPojo> autoDiscoConnections = discoveredConnectionsBeanList.parallelStream()
                .map(c -> GetConnectionPojo.builder()
                        .sourceServiceId(c.getSourceId())
                        .sourceServiceName(c.getSourceName())
                        .sourceServiceIdentifier(c.getSourceIdentifier())
                        .destinationServiceId(c.getDestinationId())
                        .destinationServiceName(c.getDestinationName())
                        .destinationServiceIdentifier(c.getDestinationIdentifier())
                        .process("Auto")
                        .status(c.getDiscoveryStatus())
                        .lastDiscoveryRunTime(c.getLastUpdatedTime())
                        .build())
                .collect(Collectors.toList());

        connections.addAll(autoDiscoConnections);

        return connections.parallelStream()
                .sorted(Comparator.comparing(GetConnectionPojo::getProcess)
                        .thenComparing(GetConnectionPojo::getLastDiscoveryRunTime, Comparator.reverseOrder()))
                .distinct().collect(Collectors.toList());
    }
}
