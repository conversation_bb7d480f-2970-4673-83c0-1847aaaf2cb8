#=====================================
# PATHS
#=====================================
server.servlet.context-path=/v2.0/api
logging.path=/opt/jboss/keycloak/standalone/log

#=====================================
# KEYCLOAK CONFIG
#KeyCloak parameters, these are used for session management
#=====================================
ds.keycloak.ip=**************
ds.keycloak.port=8443
ds.keycloak.user=appsoneadmin
ds.keycloak.pwd=QXBwc29uZUAxMjM=
ds.setup.type=
ds.filename.headers.properties=headers_details.json
ds.filename.keycloak.details=keycloak_details.json

#=====================================
# SPRING DATASOURCE CONFIG
#=====================================
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=************************************************************************************************************************************************************
spring.datasource.username=dbadmin
spring.datasource.password=cm9vdEAxMjM=
spring.datasource.hikari.connection-timeout=5000
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=5
spring.datasource.hikari.poolName=Heal_ControlCenter_Pool

#=====================================
# OPTIMIZATION
#=====================================
spring.datasource.hikari.data-source-properties.useConfigs=maxPerformance
spring.datasource.hikari.data-source-properties.rewriteBatchedStatements=true

#=======================================
# NOTIFICATION SETTINGS
#=======================================
openForLong.minDuration.time.min=15
openForTooLong.minDuration.time.min=30
openForLong.maxDuration.time.min=1440
openForTooLong.maxDuration.time.min=2880

# ======================================
# Dormant Schedular Configuration
# ======================================
user.dormant.creation.time.days=30
user.dormant.login.time.days=90

# ==========================================================
#Redis Server Configuration
# ==========================================================
spring.redis.cluster.nodes=**************:7001,**************:7002,**************:7003,**************:7004,**************:7005,**************:7006,**************:7007
spring.redis.ssl=true
spring.redis.username=
spring.redis.password=cmVkaXNAMTIz
spring.redis.max.idle.connections=500
spring.redis.min.idle.connections=100
spring.redis.max.total.connections=500
spring.redis.max.wait.secs=20
spring.redis.share.native.connection=false
spring.redis.cluster.mode=true

# ==========================================================
# Rabbit MQ Configuration
# ==========================================================
spring.rabbitmq.port=5672
spring.rabbitmq.ssl.enabled=false
spring.rabbitmq.ssl.protocol=TLSv1.2

# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/v3/api-docs
springdoc.api-docs.enabled=true
springdoc.swagger-ui.path=/swagger-ui.html
